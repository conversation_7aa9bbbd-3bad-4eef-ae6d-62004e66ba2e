#!/usr/bin/env node

/**
 * Simple test script to validate basic patent ownership tracking
 * This script tests patent upload and basic ownership fields
 */

const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Configuration
const GANACHE_URL = 'http://127.0.0.1:7545';

// Test account (from Ganache deterministic accounts)
const TEST_ACCOUNT = {
  address: '0x164D435789d02dbE8317f48017D61663C1CE369B',
  privateKey: '0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d'
};

class SimpleOwnershipTest {
  constructor() {
    this.web3 = new Web3(GANACHE_URL);
    this.contracts = {};
  }

  async initialize() {
    console.log('🚀 Initializing Simple Ownership Test...\n');
    
    // Load contract addresses
    const contractAddressesPath = path.join(__dirname, '../backend/contract-addresses.json');
    if (!fs.existsSync(contractAddressesPath)) {
      throw new Error('Contract addresses file not found. Please deploy contracts first.');
    }
    
    const contractAddresses = JSON.parse(fs.readFileSync(contractAddressesPath, 'utf8'));
    console.log('📋 Contract Addresses:');
    console.log(`   PatentRegistry: ${contractAddresses.PatentRegistry}`);
    console.log(`   UserManagement: ${contractAddresses.UserManagement}`);
    console.log();

    // Load contract ABIs and create contract instances
    const contractsPath = path.join(__dirname, '../backend/build/contracts');
    
    for (const contractName of ['PatentRegistry', 'UserManagement']) {
      const contractFile = path.join(contractsPath, `${contractName}.json`);
      const contractData = JSON.parse(fs.readFileSync(contractFile, 'utf8'));
      
      this.contracts[contractName] = new this.web3.eth.Contract(
        contractData.abi,
        contractAddresses[contractName]
      );
    }

    console.log('✅ Contracts loaded successfully\n');
  }

  async registerTestUser() {
    console.log('👤 Registering test user...');
    
    try {
      // Check if user is already registered
      const isRegistered = await this.contracts.UserManagement.methods
        .registeredUsers(TEST_ACCOUNT.address)
        .call();

      if (!isRegistered) {
        console.log(`   Registering user ${TEST_ACCOUNT.address}...`);
        
        const tx = await this.contracts.UserManagement.methods
          .registerUser('Test User', '**********')
          .send({
            from: TEST_ACCOUNT.address,
            gas: 500000
          });

        console.log(`   ✅ User registered successfully`);
      } else {
        console.log(`   ℹ️  User already registered`);
      }
    } catch (error) {
      console.log(`   ❌ Failed to register user: ${error.message}`);
      throw error;
    }
    console.log();
  }

  async uploadTestPatent() {
    console.log('📄 Uploading test patent...');
    
    const patentData = {
      name: 'Simple Test Patent',
      number: `SIMPLE-${Date.now()}`,
      category: 'Software',
      price: this.web3.utils.toWei('1', 'ether'),
      abstractText: 'This is a simple test patent for ownership validation',
      applicationDate: Math.floor(Date.now() / 1000) - 86400,
      expirationDate: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60),
      ownerName: 'Test Owner',
      ownerIdNumber: 'SIMPLE123',
      isAgentSale: false,
      documentHash: 'QmSimpleTestDoc',
      ownershipDocumentHash: 'QmSimpleTestOwnership'
    };

    try {
      const tx = await this.contracts.PatentRegistry.methods
        .uploadPatent(
          patentData.name,
          patentData.number,
          patentData.category,
          patentData.price,
          patentData.abstractText,
          patentData.applicationDate,
          patentData.expirationDate,
          patentData.ownerName,
          patentData.ownerIdNumber,
          patentData.isAgentSale,
          patentData.documentHash,
          patentData.ownershipDocumentHash
        )
        .send({
          from: TEST_ACCOUNT.address,
          gas: 1000000
        });

      // Get the patent ID from the event
      const events = await this.contracts.PatentRegistry.getPastEvents('PatentUploaded', {
        fromBlock: tx.blockNumber,
        toBlock: tx.blockNumber
      });

      if (events.length > 0) {
        this.testPatentId = events[0].returnValues.patentId;
        console.log(`   ✅ Patent uploaded successfully with ID: ${this.testPatentId}`);
        return true;
      } else {
        throw new Error('Patent upload event not found');
      }
    } catch (error) {
      console.log(`   ❌ Failed to upload patent: ${error.message}`);
      return false;
    }
  }

  async verifyOwnershipFields() {
    console.log('🔍 Verifying ownership fields...');
    
    try {
      const patent = await this.contracts.PatentRegistry.methods
        .getPatent(this.testPatentId)
        .call();

      console.log(`   📋 Patent ownership information:`);
      console.log(`      Patent ID: ${patent.id}`);
      console.log(`      Name: ${patent.name}`);
      console.log(`      Original uploader: ${patent.uploaderAddress}`);
      console.log(`      Current owner: ${patent.currentOwnerAddress}`);
      
      // Verify ownership fields
      const hasUploaderAddress = !!patent.uploaderAddress;
      const hasCurrentOwnerAddress = !!patent.currentOwnerAddress;
      const uploaderIsCorrect = patent.uploaderAddress.toLowerCase() === TEST_ACCOUNT.address.toLowerCase();
      const currentOwnerIsCorrect = patent.currentOwnerAddress.toLowerCase() === TEST_ACCOUNT.address.toLowerCase();
      const uploaderEqualsCurrentOwner = patent.uploaderAddress.toLowerCase() === patent.currentOwnerAddress.toLowerCase();
      
      console.log(`   🔍 Ownership validation:`);
      console.log(`      Has uploaderAddress: ${hasUploaderAddress ? '✅' : '❌'}`);
      console.log(`      Has currentOwnerAddress: ${hasCurrentOwnerAddress ? '✅' : '❌'}`);
      console.log(`      Uploader is test account: ${uploaderIsCorrect ? '✅' : '❌'}`);
      console.log(`      Current owner is test account: ${currentOwnerIsCorrect ? '✅' : '❌'}`);
      console.log(`      Uploader equals current owner: ${uploaderEqualsCurrentOwner ? '✅' : '❌'}`);
      
      const allChecksPass = hasUploaderAddress && hasCurrentOwnerAddress && 
                           uploaderIsCorrect && currentOwnerIsCorrect && uploaderEqualsCurrentOwner;
      
      if (allChecksPass) {
        console.log(`   ✅ All ownership field checks passed!`);
      } else {
        console.log(`   ❌ Some ownership field checks failed!`);
      }
      
      return allChecksPass;
    } catch (error) {
      console.log(`   ❌ Failed to verify ownership fields: ${error.message}`);
      return false;
    }
  }

  async testSmartContractMethods() {
    console.log('🧪 Testing smart contract methods...');
    
    try {
      // Test getUserUploadedPatents
      console.log(`   Testing getUserUploadedPatents...`);
      const uploadedPatents = await this.contracts.PatentRegistry.methods
        .getUserUploadedPatents(TEST_ACCOUNT.address)
        .call();
      
      console.log(`      Uploaded patents count: ${uploadedPatents.length}`);
      const hasTestPatent = uploadedPatents.includes(this.testPatentId);
      console.log(`      Contains test patent: ${hasTestPatent ? '✅' : '❌'}`);
      
      // Test getUserPatents (current ownership)
      console.log(`   Testing getUserPatents...`);
      const ownedPatents = await this.contracts.PatentRegistry.methods
        .getUserPatents(TEST_ACCOUNT.address)
        .call();
      
      console.log(`      Owned patents count: ${ownedPatents.length}`);
      const ownsTestPatent = ownedPatents.includes(this.testPatentId);
      console.log(`      Contains test patent: ${ownsTestPatent ? '✅' : '❌'}`);
      
      const methodsWork = hasTestPatent && ownsTestPatent;
      
      if (methodsWork) {
        console.log(`   ✅ Smart contract methods working correctly!`);
      } else {
        console.log(`   ❌ Smart contract methods have issues!`);
      }
      
      return methodsWork;
    } catch (error) {
      console.log(`   ❌ Failed to test smart contract methods: ${error.message}`);
      return false;
    }
  }

  async runTest() {
    console.log('🧪 Running Simple Patent Ownership Test\n');
    console.log('=' * 50);
    
    try {
      await this.initialize();
      await this.registerTestUser();
      
      const uploadSuccess = await this.uploadTestPatent();
      if (!uploadSuccess) {
        throw new Error('Patent upload failed');
      }
      
      console.log();
      const ownershipFieldsCorrect = await this.verifyOwnershipFields();
      console.log();
      const smartContractMethodsWork = await this.testSmartContractMethods();
      
      console.log('\n' + '=' * 50);
      console.log('📊 TEST RESULTS SUMMARY');
      console.log('=' * 50);
      console.log(`Patent Upload: ✅ PASS`);
      console.log(`Ownership Fields: ${ownershipFieldsCorrect ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Smart Contract Methods: ${smartContractMethodsWork ? '✅ PASS' : '❌ FAIL'}`);
      
      const overallSuccess = ownershipFieldsCorrect && smartContractMethodsWork;
      console.log(`Overall Test: ${overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
      
      if (overallSuccess) {
        console.log('\n🎉 Simple ownership test passed! Basic ownership tracking is working.');
        console.log('\nVerified:');
        console.log('✅ Patents have both uploaderAddress and currentOwnerAddress fields');
        console.log('✅ Initial ownership is correctly set');
        console.log('✅ Smart contract methods return correct results');
      } else {
        console.log('\n❌ Simple ownership test failed. Please check the implementation.');
      }
      
      return overallSuccess;
    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      return false;
    }
  }
}

// Main execution
async function main() {
  const test = new SimpleOwnershipTest();
  
  try {
    const success = await test.runTest();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = SimpleOwnershipTest;
