#!/usr/bin/env node

/**
 * Test script to validate patent ownership tracking fixes
 * This script tests the complete patent purchase workflow and ownership categorization
 */

const { Web3 } = require('web3');
const fs = require('fs');
const path = require('path');

// Configuration
const GANACHE_URL = 'http://127.0.0.1:7545';
const BACKEND_URL = 'http://localhost:3000';

// Test accounts (from Ganache deterministic accounts)
const TEST_ACCOUNTS = {
  uploader: {
    address: '0x164D435789d02dbE8317f48017D61663C1CE369B',
    privateKey: '0x4f3edf983ac636a65a842ce7c78d9aa706d3b113bce9c46f30d7d21715b23b1d'
  },
  buyer: {
    address: '0x2932b7A2355D6fecc4b5c0B6BD44cC31df247a2e',
    privateKey: '0x6cbed15c793ce57650b9877cf6fa156fbef513c4e6134f022a85b1ffdd59b2a1'
  },
  reviewer: {
    address: '0xfF3f8d6e471B6A81B86c6650931D8956e4277aae',
    privateKey: '0x6370fd033278c143179d81c5526140625662b8daa446c22ee2d73db3707e620c'
  }
};

class PatentOwnershipTest {
  constructor() {
    this.web3 = new Web3(GANACHE_URL);
    this.contracts = {};
    this.testResults = [];
  }

  async initialize() {
    console.log('🚀 Initializing Patent Ownership Test...\n');

    // Load contract addresses
    const contractAddressesPath = path.join(__dirname, '../backend/contract-addresses.json');
    if (!fs.existsSync(contractAddressesPath)) {
      throw new Error('Contract addresses file not found. Please deploy contracts first.');
    }

    const contractAddresses = JSON.parse(fs.readFileSync(contractAddressesPath, 'utf8'));
    console.log('📋 Contract Addresses:');
    Object.entries(contractAddresses).forEach(([name, address]) => {
      if (name !== 'network' && name !== 'deployedAt') {
        console.log(`   ${name}: ${address}`);
      }
    });
    console.log();

    // Load contract ABIs and create contract instances
    const contractsPath = path.join(__dirname, '../backend/build/contracts');

    for (const contractName of ['PatentRegistry', 'UserManagement', 'TransactionManager']) {
      const contractFile = path.join(contractsPath, `${contractName}.json`);
      const contractData = JSON.parse(fs.readFileSync(contractFile, 'utf8'));

      this.contracts[contractName] = new this.web3.eth.Contract(
        contractData.abi,
        contractAddresses[contractName]
      );
    }

    console.log('✅ Contracts loaded successfully\n');
  }

  async registerTestUsers() {
    console.log('👥 Registering test users...');

    const users = [
      { account: TEST_ACCOUNTS.uploader, name: 'Patent Uploader', phone: '**********', role: 'user' },
      { account: TEST_ACCOUNTS.buyer, name: 'Patent Buyer', phone: '**********', role: 'user' },
      { account: TEST_ACCOUNTS.reviewer, name: 'Patent Reviewer', phone: '**********', role: 'reviewer' }
    ];

    for (const user of users) {
      try {
        // Check if user is already registered
        const isRegistered = await this.contracts.UserManagement.methods
          .registeredUsers(user.account.address)
          .call();

        if (!isRegistered) {
          console.log(`   Registering ${user.name}...`);

          const tx = await this.contracts.UserManagement.methods
            .registerUser(user.name, user.phone)
            .send({
              from: user.account.address,
              gas: 500000
            });

          // Set role if not user
          if (user.role !== 'user') {
            await this.contracts.UserManagement.methods
              .setUserRole(user.account.address, user.role === 'reviewer' ? 1 : 2)
              .send({
                from: TEST_ACCOUNTS.uploader.address, // Admin account
                gas: 200000
              });
          }

          console.log(`   ✅ ${user.name} registered successfully`);
        } else {
          console.log(`   ℹ️  ${user.name} already registered`);
        }
      } catch (error) {
        console.log(`   ❌ Failed to register ${user.name}: ${error.message}`);
      }
    }
    console.log();
  }

  async uploadTestPatent() {
    console.log('📄 Uploading test patent...');

    const patentData = {
      name: 'Test Patent for Ownership Tracking',
      number: `TEST-${Date.now()}`,
      category: 'Software',
      price: this.web3.utils.toWei('1', 'ether'),
      abstractText: 'This is a test patent for validating ownership tracking fixes',
      applicationDate: Math.floor(Date.now() / 1000) - 86400, // Yesterday
      expirationDate: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60), // 1 year from now
      ownerName: 'Test Owner',
      ownerIdNumber: 'TEST123456',
      isAgentSale: false,
      documentHash: 'QmTestDocumentHash123',
      ownershipDocumentHash: 'QmTestOwnershipHash456'
    };

    try {
      const tx = await this.contracts.PatentRegistry.methods
        .uploadPatent(
          patentData.name,
          patentData.number,
          patentData.category,
          patentData.price,
          patentData.abstractText,
          patentData.applicationDate,
          patentData.expirationDate,
          patentData.ownerName,
          patentData.ownerIdNumber,
          patentData.isAgentSale,
          patentData.documentHash,
          patentData.ownershipDocumentHash
        )
        .send({
          from: TEST_ACCOUNTS.uploader.address,
          gas: 1000000
        });

      // Get the patent ID from the event
      const events = await this.contracts.PatentRegistry.getPastEvents('PatentUploaded', {
        fromBlock: tx.blockNumber,
        toBlock: tx.blockNumber
      });

      if (events.length > 0) {
        this.testPatentId = events[0].returnValues.patentId;
        console.log(`   ✅ Patent uploaded successfully with ID: ${this.testPatentId}`);

        // Verify initial ownership
        const patent = await this.contracts.PatentRegistry.methods
          .getPatent(this.testPatentId)
          .call();

        console.log(`   📋 Initial ownership:`);
        console.log(`      Original uploader: ${patent.uploaderAddress}`);
        console.log(`      Current owner: ${patent.currentOwnerAddress}`);

        if (patent.uploaderAddress === patent.currentOwnerAddress) {
          console.log(`   ✅ Initial ownership tracking correct`);
        } else {
          console.log(`   ❌ Initial ownership tracking incorrect`);
        }
      } else {
        throw new Error('Patent upload event not found');
      }
    } catch (error) {
      console.log(`   ❌ Failed to upload patent: ${error.message}`);
      throw error;
    }
    console.log();
  }

  async approvePatent() {
    console.log('✅ Approving test patent...');

    try {
      const tx = await this.contracts.PatentRegistry.methods
        .reviewPatent(this.testPatentId, 1, 'Approved for testing') // 1 = approved
        .send({
          from: TEST_ACCOUNTS.reviewer.address,
          gas: 500000
        });

      console.log(`   ✅ Patent approved successfully`);
    } catch (error) {
      console.log(`   ❌ Failed to approve patent: ${error.message}`);
      throw error;
    }
    console.log();
  }

  async createPurchaseTransaction() {
    console.log('💰 Creating purchase transaction...');

    try {
      const price = this.web3.utils.toWei('1', 'ether');

      const tx = await this.contracts.TransactionManager.methods
        .createTransaction(
          this.testPatentId,
          TEST_ACCOUNTS.uploader.address, // seller
          price
        )
        .send({
          from: TEST_ACCOUNTS.buyer.address,
          value: price,
          gas: 1000000
        });

      // Get transaction ID from event
      const events = await this.contracts.TransactionManager.getPastEvents('TransactionCreated', {
        fromBlock: tx.blockNumber,
        toBlock: tx.blockNumber
      });

      if (events.length > 0) {
        this.transactionId = events[0].returnValues.transactionId;
        console.log(`   ✅ Purchase transaction created with ID: ${this.transactionId}`);
      } else {
        throw new Error('Transaction creation event not found');
      }
    } catch (error) {
      console.log(`   ❌ Failed to create purchase transaction: ${error.message}`);
      throw error;
    }
    console.log();
  }

  async approveTransaction() {
    console.log('✅ Approving purchase transaction...');

    try {
      const tx = await this.contracts.TransactionManager.methods
        .approveTransaction(this.transactionId, 'Transaction approved for testing')
        .send({
          from: TEST_ACCOUNTS.reviewer.address,
          gas: 1000000
        });

      console.log(`   ✅ Transaction approved successfully`);
    } catch (error) {
      console.log(`   ❌ Failed to approve transaction: ${error.message}`);
      throw error;
    }
    console.log();
  }

  async verifyOwnershipTransfer() {
    console.log('🔍 Verifying ownership transfer...');

    try {
      // Get updated patent information
      const patent = await this.contracts.PatentRegistry.methods
        .getPatent(this.testPatentId)
        .call();

      console.log(`   📋 Post-transfer ownership:`);
      console.log(`      Original uploader: ${patent.uploaderAddress}`);
      console.log(`      Current owner: ${patent.currentOwnerAddress}`);

      // Verify ownership transfer
      const expectedOriginalUploader = TEST_ACCOUNTS.uploader.address;
      const expectedCurrentOwner = TEST_ACCOUNTS.buyer.address;

      const uploaderCorrect = patent.uploaderAddress.toLowerCase() === expectedOriginalUploader.toLowerCase();
      const ownerCorrect = patent.currentOwnerAddress.toLowerCase() === expectedCurrentOwner.toLowerCase();

      if (uploaderCorrect && ownerCorrect) {
        console.log(`   ✅ Ownership transfer successful!`);
        console.log(`      - Original uploader preserved: ${uploaderCorrect}`);
        console.log(`      - Current owner updated: ${ownerCorrect}`);
        return true;
      } else {
        console.log(`   ❌ Ownership transfer failed!`);
        console.log(`      - Original uploader preserved: ${uploaderCorrect}`);
        console.log(`      - Current owner updated: ${ownerCorrect}`);
        return false;
      }
    } catch (error) {
      console.log(`   ❌ Failed to verify ownership transfer: ${error.message}`);
      return false;
    }
  }

  async testPatentCategorization() {
    console.log('📊 Testing patent categorization...');

    try {
      // Test getUserUploadedPatents (should return patents originally uploaded by user)
      console.log(`   Testing getUserUploadedPatents for uploader...`);
      const uploaderPatents = await this.contracts.PatentRegistry.methods
        .getUserUploadedPatents(TEST_ACCOUNTS.uploader.address)
        .call();

      console.log(`   📋 Uploader's originally uploaded patents: ${uploaderPatents.length}`);
      const uploaderHasPatent = uploaderPatents.includes(this.testPatentId);
      console.log(`      - Contains test patent: ${uploaderHasPatent ? '✅' : '❌'}`);

      // Test getUserPatents (should return patents currently owned by user)
      console.log(`   Testing getUserPatents for buyer...`);
      const buyerOwnedPatents = await this.contracts.PatentRegistry.methods
        .getUserPatents(TEST_ACCOUNTS.buyer.address)
        .call();

      console.log(`   📋 Buyer's currently owned patents: ${buyerOwnedPatents.length}`);
      const buyerOwnsPatent = buyerOwnedPatents.includes(this.testPatentId);
      console.log(`      - Contains test patent: ${buyerOwnsPatent ? '✅' : '❌'}`);

      // Test getUserPatents for uploader (should NOT contain the sold patent)
      console.log(`   Testing getUserPatents for uploader (after sale)...`);
      const uploaderOwnedPatents = await this.contracts.PatentRegistry.methods
        .getUserPatents(TEST_ACCOUNTS.uploader.address)
        .call();

      console.log(`   📋 Uploader's currently owned patents: ${uploaderOwnedPatents.length}`);
      const uploaderStillOwns = uploaderOwnedPatents.includes(this.testPatentId);
      console.log(`      - Still contains test patent: ${uploaderStillOwns ? '❌' : '✅'}`);

      // Summary
      const categorizationCorrect = uploaderHasPatent && buyerOwnsPatent && !uploaderStillOwns;

      if (categorizationCorrect) {
        console.log(`   ✅ Patent categorization working correctly!`);
        console.log(`      - Uploader still shows as original uploader: ✅`);
        console.log(`      - Buyer now owns the patent: ✅`);
        console.log(`      - Uploader no longer owns the patent: ✅`);
      } else {
        console.log(`   ❌ Patent categorization has issues!`);
      }

      return categorizationCorrect;
    } catch (error) {
      console.log(`   ❌ Failed to test patent categorization: ${error.message}`);
      return false;
    }
  }

  async runFullTest() {
    console.log('🧪 Running Full Patent Ownership Test\n');
    console.log('=' * 60);

    try {
      await this.initialize();
      await this.registerTestUsers();
      await this.uploadTestPatent();
      await this.approvePatent();
      await this.createPurchaseTransaction();
      await this.approveTransaction();

      console.log();
      const ownershipTransferSuccess = await this.verifyOwnershipTransfer();
      console.log();
      const categorizationSuccess = await this.testPatentCategorization();

      console.log('\n' + '=' * 60);
      console.log('📊 TEST RESULTS SUMMARY');
      console.log('=' * 60);
      console.log(`Ownership Transfer: ${ownershipTransferSuccess ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`Patent Categorization: ${categorizationSuccess ? '✅ PASS' : '❌ FAIL'}`);

      const overallSuccess = ownershipTransferSuccess && categorizationSuccess;
      console.log(`Overall Test: ${overallSuccess ? '✅ PASS' : '❌ FAIL'}`);

      if (overallSuccess) {
        console.log('\n🎉 All tests passed! Patent ownership tracking is working correctly.');
        console.log('\nExpected behavior verified:');
        console.log('✅ Patents maintain original uploader information');
        console.log('✅ Current ownership is properly tracked and updated');
        console.log('✅ Patent categorization works correctly:');
        console.log('   - Uploaded Patents: Originally uploaded and still owned');
        console.log('   - Purchased Patents: Bought from others');
        console.log('   - Sold Patents: Originally uploaded but sold to others');
      } else {
        console.log('\n❌ Some tests failed. Please check the implementation.');
      }

      return overallSuccess;
    } catch (error) {
      console.error('\n💥 Test execution failed:', error.message);
      return false;
    }
  }
}

// Main execution
async function main() {
  const test = new PatentOwnershipTest();

  try {
    const success = await test.runFullTest();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = PatentOwnershipTest;
