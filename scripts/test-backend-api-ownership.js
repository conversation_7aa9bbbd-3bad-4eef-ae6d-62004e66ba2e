#!/usr/bin/env node

/**
 * Test script to validate backend API endpoints for patent ownership tracking
 * This script tests the new getUserOwnedPatents endpoint and ownership logic
 */

const axios = require('axios');

// Configuration
const BACKEND_URL = 'http://localhost:3000';

// Test accounts (from Ganache deterministic accounts)
const TEST_ACCOUNTS = {
  uploader: '******************************************',
  buyer: '******************************************',
  reviewer: '******************************************'
};

class BackendAPITest {
  constructor() {
    this.baseURL = BACKEND_URL;
    this.testPatentId = null;
    this.transactionId = null;
  }

  async makeRequest(method, endpoint, data = null, headers = {}) {
    try {
      const config = {
        method,
        url: `${this.baseURL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`API Error: ${error.response.status} - ${error.response.data.message || error.response.statusText}`);
      } else {
        throw new Error(`Network Error: ${error.message}`);
      }
    }
  }

  async testBackendConnection() {
    console.log('🔗 Testing backend connection...');
    
    try {
      const response = await this.makeRequest('GET', '/api/health');
      console.log('   ✅ Backend is running and accessible');
      return true;
    } catch (error) {
      console.log(`   ❌ Backend connection failed: ${error.message}`);
      return false;
    }
  }

  async testGetUserUploadedPatents() {
    console.log('📄 Testing getUserUploadedPatents endpoint...');
    
    try {
      const response = await this.makeRequest('GET', `/api/patents/user/${TEST_ACCOUNTS.uploader}`);
      
      console.log(`   📋 Uploader's uploaded patents: ${response.data.length}`);
      
      if (response.data.length > 0) {
        const patent = response.data[0];
        console.log(`   📄 Sample patent:`);
        console.log(`      ID: ${patent.id}`);
        console.log(`      Name: ${patent.name}`);
        console.log(`      Uploader: ${patent.uploaderAddress}`);
        console.log(`      Current Owner: ${patent.currentOwnerAddress || 'Not available'}`);
        
        this.testPatentId = patent.id;
      }
      
      return true;
    } catch (error) {
      console.log(`   ❌ Failed to get uploaded patents: ${error.message}`);
      return false;
    }
  }

  async testGetUserOwnedPatents() {
    console.log('🏠 Testing getUserOwnedPatents endpoint...');
    
    try {
      const response = await this.makeRequest('GET', `/api/patents/owned/${TEST_ACCOUNTS.uploader}`);
      
      console.log(`   📋 Uploader's owned patents: ${response.data.length}`);
      
      if (response.data.length > 0) {
        const patent = response.data[0];
        console.log(`   📄 Sample owned patent:`);
        console.log(`      ID: ${patent.id}`);
        console.log(`      Name: ${patent.name}`);
        console.log(`      Uploader: ${patent.uploaderAddress}`);
        console.log(`      Current Owner: ${patent.currentOwnerAddress || 'Not available'}`);
      }
      
      return true;
    } catch (error) {
      console.log(`   ❌ Failed to get owned patents: ${error.message}`);
      return false;
    }
  }

  async testPatentSearch() {
    console.log('🔍 Testing patent search with ownership fields...');
    
    try {
      const response = await this.makeRequest('GET', '/api/patents/search?limit=5');
      
      console.log(`   📋 Search results: ${response.data.length}`);
      
      if (response.data.length > 0) {
        const patent = response.data[0];
        console.log(`   📄 Sample search result:`);
        console.log(`      ID: ${patent.id}`);
        console.log(`      Name: ${patent.name}`);
        console.log(`      Uploader: ${patent.uploaderAddress}`);
        console.log(`      Current Owner: ${patent.currentOwnerAddress || 'Not available'}`);
        
        // Check if ownership fields are present
        const hasUploaderAddress = !!patent.uploaderAddress;
        const hasCurrentOwnerAddress = !!patent.currentOwnerAddress;
        
        console.log(`   🔍 Ownership fields check:`);
        console.log(`      Has uploaderAddress: ${hasUploaderAddress ? '✅' : '❌'}`);
        console.log(`      Has currentOwnerAddress: ${hasCurrentOwnerAddress ? '✅' : '❌'}`);
        
        return hasUploaderAddress && hasCurrentOwnerAddress;
      }
      
      return true;
    } catch (error) {
      console.log(`   ❌ Failed to search patents: ${error.message}`);
      return false;
    }
  }

  async testPatentDetails() {
    console.log('📋 Testing patent details with ownership fields...');
    
    if (!this.testPatentId) {
      console.log('   ⚠️  No test patent ID available, skipping...');
      return true;
    }
    
    try {
      const response = await this.makeRequest('GET', `/api/patents/${this.testPatentId}`);
      
      console.log(`   📄 Patent details:`);
      console.log(`      ID: ${response.data.id}`);
      console.log(`      Name: ${response.data.name}`);
      console.log(`      Uploader: ${response.data.uploaderAddress}`);
      console.log(`      Current Owner: ${response.data.currentOwnerAddress || 'Not available'}`);
      console.log(`      Uploader Name: ${response.data.uploaderName}`);
      console.log(`      Current Owner Name: ${response.data.currentOwnerName || 'Not available'}`);
      
      // Check if ownership fields are present
      const hasUploaderAddress = !!response.data.uploaderAddress;
      const hasCurrentOwnerAddress = !!response.data.currentOwnerAddress;
      const hasUploaderName = !!response.data.uploaderName;
      const hasCurrentOwnerName = !!response.data.currentOwnerName;
      
      console.log(`   🔍 Ownership fields check:`);
      console.log(`      Has uploaderAddress: ${hasUploaderAddress ? '✅' : '❌'}`);
      console.log(`      Has currentOwnerAddress: ${hasCurrentOwnerAddress ? '✅' : '❌'}`);
      console.log(`      Has uploaderName: ${hasUploaderName ? '✅' : '❌'}`);
      console.log(`      Has currentOwnerName: ${hasCurrentOwnerName ? '✅' : '❌'}`);
      
      return hasUploaderAddress && hasCurrentOwnerAddress;
    } catch (error) {
      console.log(`   ❌ Failed to get patent details: ${error.message}`);
      return false;
    }
  }

  async testEndpointComparison() {
    console.log('⚖️  Testing endpoint comparison (uploaded vs owned)...');
    
    try {
      // Get uploaded patents
      const uploadedResponse = await this.makeRequest('GET', `/api/patents/user/${TEST_ACCOUNTS.uploader}`);
      const uploadedPatents = uploadedResponse.data;
      
      // Get owned patents
      const ownedResponse = await this.makeRequest('GET', `/api/patents/owned/${TEST_ACCOUNTS.uploader}`);
      const ownedPatents = ownedResponse.data;
      
      console.log(`   📊 Comparison results:`);
      console.log(`      Uploaded patents: ${uploadedPatents.length}`);
      console.log(`      Owned patents: ${ownedPatents.length}`);
      
      // For a user who hasn't sold any patents, these should be the same
      // For a user who has sold patents, uploaded should be >= owned
      const uploadedIds = new Set(uploadedPatents.map(p => p.id));
      const ownedIds = new Set(ownedPatents.map(p => p.id));
      
      // Check if all owned patents are in uploaded patents (for original uploader)
      const allOwnedAreUploaded = [...ownedIds].every(id => uploadedIds.has(id));
      
      console.log(`   🔍 Logic check:`);
      console.log(`      All owned patents are originally uploaded: ${allOwnedAreUploaded ? '✅' : '❌'}`);
      console.log(`      Uploaded count >= Owned count: ${uploadedPatents.length >= ownedPatents.length ? '✅' : '❌'}`);
      
      return true;
    } catch (error) {
      console.log(`   ❌ Failed to compare endpoints: ${error.message}`);
      return false;
    }
  }

  async runAllTests() {
    console.log('🧪 Running Backend API Ownership Tests\n');
    console.log('=' * 60);
    
    const tests = [
      { name: 'Backend Connection', test: () => this.testBackendConnection() },
      { name: 'Get User Uploaded Patents', test: () => this.testGetUserUploadedPatents() },
      { name: 'Get User Owned Patents', test: () => this.testGetUserOwnedPatents() },
      { name: 'Patent Search', test: () => this.testPatentSearch() },
      { name: 'Patent Details', test: () => this.testPatentDetails() },
      { name: 'Endpoint Comparison', test: () => this.testEndpointComparison() }
    ];
    
    const results = [];
    
    for (const { name, test } of tests) {
      console.log();
      try {
        const success = await test();
        results.push({ name, success });
        console.log(`   ${success ? '✅' : '❌'} ${name}: ${success ? 'PASS' : 'FAIL'}`);
      } catch (error) {
        results.push({ name, success: false });
        console.log(`   ❌ ${name}: FAIL (${error.message})`);
      }
    }
    
    console.log('\n' + '=' * 60);
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('=' * 60);
    
    results.forEach(({ name, success }) => {
      console.log(`${success ? '✅' : '❌'} ${name}: ${success ? 'PASS' : 'FAIL'}`);
    });
    
    const passedTests = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n📈 Overall: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 All API tests passed! Backend ownership tracking is working correctly.');
    } else {
      console.log('\n⚠️  Some API tests failed. Please check the backend implementation.');
    }
    
    return passedTests === totalTests;
  }
}

// Main execution
async function main() {
  const test = new BackendAPITest();
  
  try {
    const success = await test.runAllTests();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  main();
}

module.exports = BackendAPITest;
