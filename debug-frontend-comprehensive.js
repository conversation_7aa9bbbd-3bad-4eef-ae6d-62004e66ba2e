const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

/**
 * Comprehensive frontend request simulation and debugging
 * This replicates the exact frontend request with detailed analysis
 */

async function createTestFiles() {
  // Create test files with proper content that simulates PDF files
  // We'll create binary content that mimics a PDF structure
  const pdfHeader = Buffer.from('%PDF-1.4\n');
  const patentContent = Buffer.from(`
    Patent Document Test Content
    ===========================

    This is a test patent document for debugging purposes.
    It contains sufficient content to simulate a real patent document.

    Patent Title: Test Patent for Upload Debugging
    Inventor: Test User
    Application Date: 2023-01-15

    Technical Field:
    This patent relates to testing and debugging patent upload functionality.

    Background:
    Patent upload systems require proper validation and file handling.

    Summary:
    This test patent demonstrates the upload process and helps identify issues.
  `);

  const ownershipContent = Buffer.from(`
    Patent Ownership Certificate
    ===========================

    This document certifies ownership of the test patent.

    Owner: Test User
    ID Number: 110101199001011234
    Patent Number: CN202410001234.5

    This certificate is issued for testing purposes only.
  `);

  // Create files with PDF extension and basic PDF structure
  const patentPdf = Buffer.concat([pdfHeader, patentContent]);
  const ownershipPdf = Buffer.concat([pdfHeader, ownershipContent]);

  fs.writeFileSync('test-patent-doc.pdf', patentPdf);
  fs.writeFileSync('test-ownership-doc.pdf', ownershipPdf);

  console.log('✅ Test PDF files created');
}

async function simulateExactFrontendRequest() {
  console.log('🔍 Simulating exact frontend request...');

  try {
    // Create test files
    await createTestFiles();

    // Create FormData exactly like the frontend
    const formData = new FormData();

    // Simulate form data from frontend (with realistic values)
    const frontendForm = {
      patentName: '测试专利名称 - 前端调试专用',
      patentNumber: `CN${Date.now().toString().slice(-12)}.5`,
      patentCategory: 'invention',
      transferPrice: '1.5',
      patentAbstract: '这是一个用于前端调试的测试专利摘要。该专利描述了一种新型的技术解决方案，具有创新性和实用性。摘要内容需要达到最少50个字符的要求，以满足后端验证规则。',
      applicationDate: '2023-01-15',
      expirationDate: '2043-01-15',
      ownerName: '测试用户',
      ownerIdNumber: '110101199001011234',
      isAgentSale: false
    };

    // Add form data exactly like frontend does
    formData.append('patentName', frontendForm.patentName);
    formData.append('patentNumber', frontendForm.patentNumber);
    formData.append('patentCategory', frontendForm.patentCategory);
    formData.append('transferPrice', frontendForm.transferPrice);
    formData.append('patentAbstract', frontendForm.patentAbstract);
    formData.append('applicationDate', frontendForm.applicationDate);
    formData.append('expirationDate', frontendForm.expirationDate);
    formData.append('ownerName', frontendForm.ownerName);
    formData.append('ownerIdNumber', frontendForm.ownerIdNumber);
    formData.append('isAgentSale', frontendForm.isAgentSale.toString()); // Apply the fix
    formData.append('uploaderAddress', '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252');

    // Add files exactly like frontend does
    const patentFile = fs.createReadStream('test-patent-doc.pdf');
    const ownershipFile = fs.createReadStream('test-ownership-doc.pdf');

    formData.append('patentDocument', patentFile, {
      filename: 'test-patent-doc.pdf',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', ownershipFile, {
      filename: 'test-ownership-doc.pdf',
      contentType: 'application/pdf'
    });

    console.log('📋 FormData prepared with frontend simulation:');
    console.log('   patentName:', frontendForm.patentName);
    console.log('   patentNumber:', frontendForm.patentNumber);
    console.log('   patentCategory:', frontendForm.patentCategory);
    console.log('   transferPrice:', frontendForm.transferPrice);
    console.log('   isAgentSale:', frontendForm.isAgentSale.toString(), '(converted to string)');
    console.log('   Files: patent document and ownership document attached');

    // Make request exactly like frontend API client
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252'
      },
      timeout: 30000
    });

    console.log('✅ Frontend simulation successful:', response.data);
    return { success: true, data: response.data };

  } catch (error) {
    console.log('❌ Frontend simulation failed:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data);

    if (error.response?.status === 400 && error.response.data.error?.code === 'VALIDATION_ERROR') {
      console.log('\n🔍 Detailed validation errors:');
      if (error.response.data.error.details?.errors) {
        error.response.data.error.details.errors.forEach((err, index) => {
          console.log(`   ${index + 1}. Field: ${err.path || err.param || 'unknown'}`);
          console.log(`      Error: ${err.msg || err.message}`);
          console.log(`      Value: ${err.value || 'not provided'}`);
        });
      }
    }

    return { success: false, error: error.response?.data || error.message };
  } finally {
    // Clean up test files
    try {
      fs.unlinkSync('test-patent-doc.pdf');
      fs.unlinkSync('test-ownership-doc.pdf');
      console.log('🧹 Test files cleaned up');
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

async function testEmptyFieldValidation() {
  console.log('\n🔍 Testing empty field validation...');

  try {
    const formData = new FormData();

    // Test with empty required fields
    formData.append('patentName', '');
    formData.append('patentNumber', '');
    formData.append('patentCategory', '');
    formData.append('transferPrice', '');
    formData.append('patentAbstract', '');
    formData.append('applicationDate', '');
    formData.append('expirationDate', '');
    formData.append('ownerName', '');
    formData.append('ownerIdNumber', '');
    formData.append('isAgentSale', 'false');
    formData.append('uploaderAddress', '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252');

    // Add empty files
    formData.append('patentDocument', Buffer.from(''), {
      filename: '',
      contentType: 'application/octet-stream'
    });
    formData.append('ownershipDocument', Buffer.from(''), {
      filename: '',
      contentType: 'application/octet-stream'
    });

    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252'
      },
      timeout: 30000
    });

    console.log('❌ Expected validation error but got success');

  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Got expected validation error for empty fields');
      if (error.response.data.error?.details?.errors) {
        console.log(`   Found ${error.response.data.error.details.errors.length} validation errors`);
      }
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
}

// Run comprehensive tests
async function runComprehensiveTests() {
  console.log('🚀 Starting comprehensive frontend debugging...\n');

  const result = await simulateExactFrontendRequest();
  await testEmptyFieldValidation();

  console.log('\n📊 Test Summary:');
  if (result.success) {
    console.log('✅ Frontend simulation successful - the fix appears to be working');
  } else {
    console.log('❌ Frontend simulation failed - additional issues need to be addressed');
    console.log('❌ Error details:', result.error);
  }

  console.log('\n✅ Comprehensive tests completed');
}

runComprehensiveTests().catch(console.error);
