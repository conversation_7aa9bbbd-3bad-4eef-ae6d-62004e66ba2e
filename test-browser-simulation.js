const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

/**
 * Browser-like test simulation for patent upload
 * This replicates exactly what happens when a user fills out the form
 */

async function createRealPDFFiles() {
  // Create more realistic PDF content
  const pdfHeader = '%PDF-1.4\n';
  const pdfFooter = '\n%%EOF';
  
  const patentContent = `${pdfHeader}
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
>>
endobj

Patent Document Content:
========================

Patent Title: 一种新型智能手机充电技术
Patent Number: CN202410001234.5
Inventor: 测试用户
Application Date: 2023-01-15

Technical Field:
本发明涉及智能手机充电技术领域，特别是一种新型的无线充电解决方案。

Background:
现有的智能手机充电技术存在充电速度慢、发热量大等问题。

Summary:
本发明提供了一种新型的智能手机充电技术，能够有效提高充电效率，降低发热量。

Detailed Description:
该技术采用先进的电磁感应原理，通过优化线圈设计和控制算法，实现快速、安全的无线充电。
${pdfFooter}`;

  const ownershipContent = `${pdfHeader}
Patent Ownership Certificate
============================

Certificate Number: OWN-2024-001234
Patent Number: CN202410001234.5
Owner Name: 测试用户
Owner ID: 110101199001011234
Issue Date: 2024-01-15

This certificate confirms the ownership of the above-mentioned patent.
The owner has full rights to transfer, license, or otherwise dispose of this patent.

Issued by: Patent Office
Signature: [Digital Signature]
${pdfFooter}`;

  fs.writeFileSync('realistic-patent.pdf', patentContent);
  fs.writeFileSync('realistic-ownership.pdf', ownershipContent);
  
  console.log('✅ Realistic PDF files created');
}

async function simulateBrowserFormSubmission() {
  console.log('🌐 Simulating browser form submission...');
  
  try {
    await createRealPDFFiles();
    
    // Simulate the exact form data a user would enter
    const userFormData = {
      patentName: '一种新型智能手机充电技术',
      patentNumber: `CN${Date.now().toString().slice(-12)}.5`,
      patentCategory: 'invention',
      transferPrice: '50000',
      patentAbstract: '本发明提供了一种新型的智能手机无线充电技术，能够有效提高充电效率，降低发热量。该技术采用先进的电磁感应原理，通过优化线圈设计和控制算法，实现快速、安全的无线充电。技术具有创新性和实用性，适用于各种智能手机设备。',
      applicationDate: '2023-01-15',
      expirationDate: '2043-01-15',
      ownerName: '测试用户',
      ownerIdNumber: '110101199001011234',
      isAgentSale: false
    };
    
    // Create FormData exactly like browser would
    const formData = new FormData();
    
    // Add all form fields
    Object.entries(userFormData).forEach(([key, value]) => {
      if (key === 'isAgentSale') {
        // Apply the fix: convert boolean to string
        formData.append(key, value.toString());
      } else {
        formData.append(key, value);
      }
    });
    
    formData.append('uploaderAddress', '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252');
    
    // Add files like browser would
    const patentFile = fs.createReadStream('realistic-patent.pdf');
    const ownershipFile = fs.createReadStream('realistic-ownership.pdf');
    
    formData.append('patentDocument', patentFile, {
      filename: 'realistic-patent.pdf',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', ownershipFile, {
      filename: 'realistic-ownership.pdf',
      contentType: 'application/pdf'
    });
    
    console.log('📋 Browser-like form data prepared:');
    console.log('   Patent Name:', userFormData.patentName);
    console.log('   Patent Number:', userFormData.patentNumber);
    console.log('   Category:', userFormData.patentCategory);
    console.log('   Price:', userFormData.transferPrice);
    console.log('   Is Agent Sale:', userFormData.isAgentSale.toString());
    console.log('   Files: PDF documents attached');
    
    // Make request with browser-like headers
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        ...formData.getHeaders(),
        'X-User-Address': '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Origin': 'http://localhost:5174',
        'Referer': 'http://localhost:5174/'
      },
      timeout: 30000
    });
    
    console.log('✅ Browser simulation successful!');
    console.log('Response:', {
      success: response.data.success,
      patentId: response.data.data?.patentId,
      status: response.data.data?.status,
      message: response.data.message
    });
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ Browser simulation failed:');
    console.log('Status:', error.response?.status);
    console.log('Error Code:', error.response?.data?.error?.code);
    console.log('Error Message:', error.response?.data?.error?.message);
    
    if (error.response?.status === 400 && error.response.data.error?.code === 'VALIDATION_ERROR') {
      console.log('\n🔍 Validation errors found:');
      if (error.response.data.error.details?.errors) {
        error.response.data.error.details.errors.forEach((err, index) => {
          console.log(`   ${index + 1}. Field: ${err.path || err.param}`);
          console.log(`      Error: ${err.msg || err.message}`);
          console.log(`      Value: ${JSON.stringify(err.value)}`);
        });
      }
    }
    
    return { success: false, error: error.response?.data || error.message };
  } finally {
    // Clean up
    try {
      fs.unlinkSync('realistic-patent.pdf');
      fs.unlinkSync('realistic-ownership.pdf');
      console.log('🧹 Test files cleaned up');
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

async function testValidationEdgeCases() {
  console.log('\n🔍 Testing validation edge cases...');
  
  const testCases = [
    {
      name: 'Short patent name',
      data: { patentName: 'Test' }, // Too short (< 5 chars)
      expectedError: 'Patent name must be between 5 and 200 characters'
    },
    {
      name: 'Invalid patent number',
      data: { patentNumber: 'INVALID123' }, // Wrong format
      expectedError: 'Invalid patent number format'
    },
    {
      name: 'Short abstract',
      data: { patentAbstract: 'Too short' }, // < 50 chars
      expectedError: 'Patent abstract must be between 50 and 2000 characters'
    },
    {
      name: 'Invalid transfer price',
      data: { transferPrice: '0' }, // Must be > 0
      expectedError: 'Transfer price must be greater than 0'
    }
  ];
  
  for (const testCase of testCases) {
    try {
      const formData = new FormData();
      
      // Default valid data
      const defaultData = {
        patentName: '测试专利名称 - 边界测试',
        patentNumber: `CN${Date.now().toString().slice(-12)}.5`,
        patentCategory: 'invention',
        transferPrice: '1000',
        patentAbstract: '这是一个测试专利的摘要，用于测试验证边界情况。摘要内容需要达到最少50个字符的要求，以满足后端验证规则。',
        applicationDate: '2023-01-15',
        expirationDate: '2043-01-15',
        ownerName: '测试用户',
        ownerIdNumber: '110101199001011234',
        isAgentSale: 'false',
        uploaderAddress: '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252'
      };
      
      // Override with test case data
      const testData = { ...defaultData, ...testCase.data };
      
      // Add all data to form
      Object.entries(testData).forEach(([key, value]) => {
        formData.append(key, value);
      });
      
      // Add dummy files
      formData.append('patentDocument', Buffer.from('dummy'), {
        filename: 'test.pdf',
        contentType: 'application/pdf'
      });
      formData.append('ownershipDocument', Buffer.from('dummy'), {
        filename: 'test.pdf',
        contentType: 'application/pdf'
      });
      
      const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
        headers: {
          ...formData.getHeaders(),
          'X-User-Address': '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252'
        },
        timeout: 10000
      });
      
      console.log(`   ❌ ${testCase.name}: Expected error but got success`);
      
    } catch (error) {
      if (error.response?.status === 400) {
        console.log(`   ✅ ${testCase.name}: Got expected validation error`);
      } else {
        console.log(`   ❌ ${testCase.name}: Unexpected error - ${error.message}`);
      }
    }
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
}

// Run all tests
async function runBrowserTests() {
  console.log('🚀 Starting browser simulation tests...\n');
  
  const result = await simulateBrowserFormSubmission();
  await testValidationEdgeCases();
  
  console.log('\n📊 Browser Test Summary:');
  if (result.success) {
    console.log('✅ Browser simulation successful');
    console.log('✅ Patent upload should work correctly in the frontend');
    console.log('✅ All fixes have been applied successfully');
  } else {
    console.log('❌ Browser simulation failed');
    console.log('❌ Additional issues may need to be addressed');
  }
  
  console.log('\n✅ Browser tests completed');
}

runBrowserTests().catch(console.error);
