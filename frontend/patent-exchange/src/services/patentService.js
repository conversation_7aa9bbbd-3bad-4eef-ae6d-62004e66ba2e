import api from './apiClient.js'

// Patent service for managing patent information and operations
export const patentService = {
  // Get patent details by ID
  async getPatentDetails(patentId) {
    try {
      const response = await api.patents.getById(patentId)
      return response.data.data.data || response.data.data
    } catch (error) {
      console.error('获取专利详情失败:', error)
      throw new Error(error.message || '获取专利详情失败')
    }
  },

  // Get patent transaction history
  async getPatentTransactionHistory(patentId) {
    try {
      // Get patent details which includes transaction history
      const patentDetails = await this.getPatentDetails(patentId)
      return patentDetails.transactionHistory || []
    } catch (error) {
      console.error('获取专利交易历史失败:', error)
      throw new Error(error.message || '获取专利交易历史失败')
    }
  },

  // Search patents
  async searchPatents(searchParams) {
    try {
      const response = await api.patents.search(searchParams)
      return response.data.data
    } catch (error) {
      console.error('搜索专利失败:', error)
      throw new Error(error.message || '搜索专利失败')
    }
  },

  // Download patent document
  async downloadPatentDocument(patentId, documentType = 'patent') {
    try {
      const response = await api.patents.download(patentId, documentType)

      // Create blob URL and trigger download
      const blob = new Blob([response.data])
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `patent_${patentId}_${documentType}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      return {
        success: true,
        message: '文档下载成功'
      }
    } catch (error) {
      console.error('下载专利文档失败:', error)
      throw new Error(error.message || '下载专利文档失败')
    }
  },

  // View patent document (returns URL for viewing)
  async viewPatentDocument(patentId, documentType = 'patent') {
    try {
      const response = await api.patents.view(patentId, documentType)
      return response.data.data
    } catch (error) {
      console.error('获取专利文档URL失败:', error)
      throw new Error(error.message || '获取专利文档URL失败')
    }
  },

  // Get user's uploaded patents (originally uploaded by user)
  async getUserUploadedPatents(userAddress) {
    try {
      const response = await api.patents.getUserPatents(userAddress)
      return response.data.data
    } catch (error) {
      console.error('获取用户上传专利失败:', error)
      throw new Error(error.message || '获取用户上传专利失败')
    }
  },

  // Get user's currently owned patents
  async getUserOwnedPatents(userAddress) {
    try {
      const response = await api.patents.getUserOwnedPatents(userAddress)
      return response.data.data
    } catch (error) {
      console.error('获取用户拥有专利失败:', error)
      throw new Error(error.message || '获取用户拥有专利失败')
    }
  },

  // Get user's purchased patents
  async getUserPurchasedPatents(userAddress) {
    try {
      // Get ALL user transactions (both purchases and sales) to properly categorize them
      const response = await api.transactions.getUserTransactions(userAddress)
      return response.data.data.transactions || []
    } catch (error) {
      console.error('获取用户交易记录失败:', error)
      throw new Error(error.message || '获取用户交易记录失败')
    }
  },

  // Withdraw patent (make it unavailable for trading)
  async withdrawPatent(patentId, reason = '') {
    try {
      const response = await api.patents.withdraw(patentId, { reason })
      return response.data
    } catch (error) {
      console.error('撤回专利失败:', error)
      throw new Error(error.message || '撤回专利失败')
    }
  },

  // Freeze patent (temporarily make it non-tradable)
  async freezePatent(patentId) {
    try {
      // Note: This might be handled by withdraw with a specific reason
      const response = await api.patents.withdraw(patentId, { reason: 'Temporary freeze' })
      return response.data
    } catch (error) {
      console.error('冻结专利失败:', error)
      throw new Error(error.message || '冻结专利失败')
    }
  },

  // Restore patent (make it tradable again)
  async restorePatent(patentId) {
    try {
      const response = await api.patents.restore(patentId)
      return response.data
    } catch (error) {
      console.error('恢复专利失败:', error)
      throw new Error(error.message || '恢复专利失败')
    }
  },

  // Upload patent
  async uploadPatent(formData) {
    try {
      const response = await api.patents.upload(formData)
      return response.data
    } catch (error) {
      console.error('上传专利失败:', error)
      throw new Error(error.message || '上传专利失败')
    }
  }
}
