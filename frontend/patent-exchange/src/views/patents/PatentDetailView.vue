<template>
  <div class="patent-detail-view">
    <div class="container-fluid py-4">
      <!-- Back Button -->
      <div class="mb-3">
        <button class="btn btn-outline-secondary" @click="goBack">
          <i class="bi bi-arrow-left me-2"></i>
          返回
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="text-muted mt-3">正在加载专利详情...</p>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Patent Details -->
      <div v-else-if="patent" class="row">
        <!-- Main Patent Information -->
        <div class="col-lg-8" v-if="!isLoading">
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
              <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                  <i class="bi bi-file-earmark-text me-2"></i>
                  {{ patent.name || '专利名称' }}
                </h4>
                <span class="badge bg-light text-dark">{{ patent.category || '未分类' }}</span>
              </div>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利号</label>
                  <div class="fw-bold">{{ patent.number || 'N/A' }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">转让价格</label>
                  <div class="fw-bold text-success">¥{{ formatPrice(patent.price || 0) }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">申请日期</label>
                  <div class="fw-bold">{{ patent.applicationDate ? formatDate(patent.applicationDate) : 'N/A' }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利权结束日期</label>
                  <div class="fw-bold">{{ patent.expiryDate ? formatDate(patent.expiryDate) : 'N/A' }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利权人</label>
                  <div class="fw-bold">{{ patent.ownerName || 'N/A' }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">上传者地址</label>
                  <div v-if="patent.uploaderAddress">
                    <button
                      class="btn btn-link p-0 text-decoration-none"
                      @click="showUserDetails(patent.uploaderAddress)"
                    >
                      {{ formatAddress(patent.uploaderAddress) }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </button>
                  </div>
                  <div v-else class="fw-bold">N/A</div>
                </div>
                <div class="col-12">
                  <label class="form-label text-muted small">专利摘要</label>
                  <div class="border rounded p-3 bg-light">
                    {{ patent.abstract || '暂无摘要信息' }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Blockchain Information -->
          <div class="card shadow-sm mb-4" v-if="patent && patent.blockchain">
            <div class="card-header bg-info text-white">
              <h5 class="mb-0">
                <i class="bi bi-link-45deg me-2"></i>
                区块链信息
              </h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label text-muted small">交易哈希</label>
                  <div class="d-flex align-items-center">
                    <code class="flex-grow-1 me-2">{{ patent.blockchain?.transactionHash || 'N/A' }}</code>
                    <button class="btn btn-sm btn-outline-secondary" @click="copyToClipboard(patent.blockchain?.transactionHash || '')" title="复制" :disabled="!patent.blockchain?.transactionHash">
                      <i class="bi bi-clipboard"></i>
                    </button>
                  </div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">区块号</label>
                  <div class="fw-bold">{{ patent.blockchain?.blockNumber || 'N/A' }}</div>
                </div>
                <div class="col-12">
                  <label class="form-label text-muted small">智能合约地址</label>
                  <div class="d-flex align-items-center">
                    <code class="flex-grow-1 me-2">{{ patent.blockchain?.contractAddress || 'N/A' }}</code>
                    <button class="btn btn-sm btn-outline-secondary" @click="copyToClipboard(patent.blockchain?.contractAddress || '')" title="复制" :disabled="!patent.blockchain?.contractAddress">
                      <i class="bi bi-clipboard"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Uploader Information -->
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-warning text-dark">
              <h5 class="mb-0">
                <i class="bi bi-person-circle me-2"></i>
                上传者信息
              </h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label text-muted small">姓名</label>
                  <div class="fw-bold">{{ patent.uploaderName || 'N/A' }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">联系电话</label>
                  <div class="fw-bold">{{ patent.uploaderPhone || 'N/A' }}</div>
                </div>
                <div class="col-12">
                  <label class="form-label text-muted small">区块链地址</label>
                  <div v-if="patent.uploaderAddress">
                    <button
                      class="btn btn-link p-0 text-decoration-none"
                      @click="showUserDetails(patent.uploaderAddress)"
                    >
                      <code>{{ patent.uploaderAddress }}</code>
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </button>
                  </div>
                  <div v-else class="fw-bold">N/A</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Proxy Status Section -->
          <div class="card shadow-sm mb-4">
            <div class="card-header" :class="patent.isProxySale ? 'bg-secondary text-white' : 'bg-success text-white'">
              <h5 class="mb-0">
                <i class="bi bi-shield-check me-2"></i>
                {{ patent.isProxySale ? '代理出售' : '直接出售' }}
              </h5>
            </div>
            <div class="card-body">
              <div class="d-flex align-items-center justify-content-between">
                <div>
                  <h6 class="mb-1">{{ patent.isProxySale ? '代理委托证明文档' : '专利权证明文档' }}</h6>
                  <small class="text-muted">PDF格式，可下载查看</small>
                </div>
                <div>
                  <button class="btn btn-outline-primary btn-sm me-2" @click="viewDocument(patent.isProxySale ? 'proxy' : 'certificate')">
                    <i class="bi bi-eye me-1"></i>
                    查看
                  </button>
                  <button class="btn btn-primary btn-sm" @click="downloadDocument(patent.isProxySale ? 'proxy' : 'certificate')">
                    <i class="bi bi-download me-1"></i>
                    下载
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Patent Documents -->
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
              <h5 class="mb-0">
                <i class="bi bi-file-earmark-pdf me-2"></i>
                可下载文档
              </h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <div class="d-flex align-items-center justify-content-between p-3 border rounded">
                    <div>
                      <h6 class="mb-1">专利文档</h6>
                      <small class="text-muted">PDF格式</small>
                    </div>
                    <div>
                      <button class="btn btn-outline-primary btn-sm me-2" @click="viewDocument('patent')">
                        <i class="bi bi-eye me-1"></i>
                        查看
                      </button>
                      <button class="btn btn-primary btn-sm" @click="downloadDocument('patent')">
                        <i class="bi bi-download me-1"></i>
                        下载
                      </button>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="d-flex align-items-center justify-content-between p-3 border rounded">
                    <div>
                      <h6 class="mb-1">{{ patent.isProxySale ? '代理委托证明' : '专利权证明' }}</h6>
                      <small class="text-muted">PDF格式</small>
                    </div>
                    <div>
                      <button class="btn btn-outline-primary btn-sm me-2" @click="viewDocument('certificate')">
                        <i class="bi bi-eye me-1"></i>
                        查看
                      </button>
                      <button class="btn btn-primary btn-sm" @click="downloadDocument('certificate')">
                        <i class="bi bi-download me-1"></i>
                        下载
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Transaction History -->
          <div class="card shadow-sm mb-4">
            <div class="card-header bg-dark text-white">
              <h5 class="mb-0">
                <i class="bi bi-clock-history me-2"></i>
                交易历史
              </h5>
            </div>
            <div class="card-body">
              <div v-if="patent && patent.transactionHistory && patent.transactionHistory.length > 0" class="timeline">
                <div v-for="(transaction, index) in patent.transactionHistory" :key="transaction.id || index" class="timeline-item">
                  <div class="timeline-marker">
                    <i class="bi bi-circle-fill text-success"></i>
                  </div>
                  <div class="timeline-content">
                    <div class="d-flex justify-content-between align-items-start">
                      <div class="flex-grow-1">
                        <h6 class="mb-1">{{ transaction.description || '专利转让' }}</h6>
                        <div class="row g-2 mb-2">
                          <div class="col-md-6">
                            <small class="text-muted">转让价格</small>
                            <div class="fw-bold text-success">¥{{ formatPrice(transaction.price) }}</div>
                          </div>
                          <div class="col-md-6">
                            <small class="text-muted">交易时间</small>
                            <div class="small">{{ formatDate(transaction.timestamp) }}</div>
                          </div>
                          <div class="col-md-6">
                            <small class="text-muted">买方</small>
                            <div class="small">
                              {{ transaction.buyerName }}
                              <code class="ms-1 small">{{ transaction.buyerAddress }}</code>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <small class="text-muted">卖方</small>
                            <div class="small">
                              {{ transaction.sellerName }}
                              <code class="ms-1 small">{{ transaction.sellerAddress }}</code>
                            </div>
                          </div>
                        </div>
                        <p class="mb-0">
                          <small class="text-muted">
                            交易哈希:
                            <code class="ms-1">{{ transaction.transactionHash }}</code>
                            <button
                              class="btn btn-sm btn-outline-secondary ms-2"
                              @click="copyToClipboard(transaction.transactionHash)"
                              :disabled="!transaction.transactionHash || transaction.transactionHash === 'N/A'"
                            >
                              <i class="bi bi-clipboard"></i>
                            </button>
                          </small>
                        </p>
                      </div>
                      <span class="badge bg-success ms-3">{{ transaction.status === 'completed' ? '已完成' : '进行中' }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="text-center py-3">
                <i class="bi bi-clock-history text-muted" style="font-size: 2rem;"></i>
                <p class="text-muted mt-2 mb-0">暂无交易历史</p>
                <small class="text-muted">该专利尚未进行过转让交易</small>
              </div>
            </div>
          </div>

          <!-- Ownership and Status Information -->
          <div class="card shadow-sm mb-4">
            <div class="card-header" :class="getStatusBadgeClass(patent.status)">
              <h5 class="mb-0 text-white">
                <i class="bi bi-info-circle me-2"></i>
                专利状态: {{ getStatusText(patent.status) }}
              </h5>
            </div>
            <div class="card-body">
              <div class="row g-3">
                <div class="col-md-6">
                  <label class="form-label text-muted small">当前专利权人</label>
                  <div class="fw-bold">{{ patent.ownerName || 'N/A' }}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label text-muted small">专利权人地址</label>
                  <div v-if="patent.uploaderAddress">
                    <button
                      class="btn btn-link p-0 text-decoration-none"
                      @click="showUserDetails(patent.uploaderAddress)"
                    >
                      {{ formatAddress(patent.uploaderAddress) }}
                      <i class="bi bi-box-arrow-up-right ms-1"></i>
                    </button>
                  </div>
                  <div v-else class="fw-bold">N/A</div>
                </div>
              </div>

              <!-- Status-specific information -->
              <div v-if="patent.status === 'trading'" class="mt-3">
                <div class="alert alert-warning" role="alert">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  该专利正在交易过程中，暂时无法进行新的购买申请。
                </div>
              </div>

              <div v-if="patent.status === 'pending'" class="mt-3">
                <div class="alert alert-info" role="alert">
                  <i class="bi bi-clock me-2"></i>
                  该专利正在等待审核，通过审核后方可进行交易。
                </div>
              </div>

              <!-- Pending Transaction Information -->
              <div v-if="patent.status === 'pending_transaction' && patent.pendingTransaction" class="mt-3">
                <div class="alert alert-warning" role="alert">
                  <i class="bi bi-clock-history me-2"></i>
                  <strong>交易审核中</strong>
                  <div class="mt-2 small">
                    <div class="row g-2">
                      <div class="col-6">
                        <strong>买方:</strong> {{ patent.pendingTransaction.buyerName }}
                      </div>
                      <div class="col-6">
                        <strong>交易价格:</strong> ¥{{ formatPrice(patent.pendingTransaction.price) }}
                      </div>
                      <div class="col-12">
                        <strong>提交时间:</strong> {{ formatDate(patent.pendingTransaction.submitDate) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Transaction Completed Information -->
              <div v-if="patent.status === 'transaction_completed'" class="mt-3">
                <div class="alert alert-success" role="alert">
                  <i class="bi bi-check-circle-fill me-2"></i>
                  <strong>交易已完成 - 专利权转移成功</strong>
                  <div class="mt-2 small">
                    <strong>恭喜！</strong> 此专利的交易已完成，专利权已正式转移。
                  </div>
                  <div class="mt-3">
                    <button class="btn btn-success btn-sm me-2" @click="downloadContract(patent.id)">
                      <i class="bi bi-file-earmark-check me-1"></i>
                      下载转让证书
                    </button>
                    <button class="btn btn-outline-primary btn-sm" @click="refreshPatentStatus">
                      <i class="bi bi-arrow-clockwise me-1"></i>
                      刷新信息
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Panel -->
        <div class="col-lg-4" v-if="patent">
          <!-- Patent Status -->
          <div class="card shadow-sm mb-3">
            <div class="card-body text-center">
              <div class="mb-3">
                <span :class="getStatusBadgeClass(patent.status)" class="badge fs-6 px-3 py-2">
                  {{ getStatusText(patent.status) }}
                </span>
              </div>
              <h3 class="text-success mb-3">¥{{ formatPrice(patent.price || 0) }}</h3>

              <!-- Action Buttons -->
              <div class="d-grid gap-2" v-if="canTrade">
                <button
                  class="btn btn-success btn-lg"
                  @click="initiateTransaction"
                  :disabled="isTransacting"
                >
                  <span v-if="isTransacting" class="spinner-border spinner-border-sm me-2"></span>
                  <i v-else class="bi bi-cart-plus me-2"></i>
                  {{ isTransacting ? '处理中...' : '购买专利' }}
                </button>
                <button class="btn btn-outline-warning" @click="initiateRightsProtection">
                  <i class="bi bi-shield-exclamation me-2"></i>
                  专利维权
                </button>
              </div>

              <div v-else class="alert alert-warning" role="alert">
                <small>{{ getUnavailableReason(patent.status) }}</small>
              </div>
            </div>
          </div>

          <!-- Patent Statistics -->
          <div class="card shadow-sm mb-3">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-graph-up me-2"></i>
                浏览统计
              </h6>
            </div>
            <div class="card-body">
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">浏览次数</span>
                <span class="fw-bold">{{ patent.viewCount || 0 }}</span>
              </div>
              <div class="d-flex justify-content-between">
                <span class="text-muted">下载次数</span>
                <span class="fw-bold">{{ patent.downloadCount || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- Upload Information -->
          <div class="card shadow-sm">
            <div class="card-header">
              <h6 class="mb-0">
                <i class="bi bi-info-circle me-2"></i>
                上传信息
              </h6>
            </div>
            <div class="card-body">
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">上传时间</span>
                <span class="fw-bold">{{ formatDate(patent.uploadDate) }}</span>
              </div>
              <div class="d-flex justify-content-between mb-2">
                <span class="text-muted">代理出售</span>
                <span class="fw-bold">{{ patent.isProxySale ? '是' : '否' }}</span>
              </div>
              <div class="d-flex justify-content-between">
                <span class="text-muted">专利状态</span>
                <span :class="getStatusBadgeClass(patent.status)" class="badge">
                  {{ getStatusText(patent.status) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Patent Found -->
      <div v-else-if="!isLoading && !error" class="text-center py-5">
        <i class="bi bi-file-earmark-x text-muted" style="font-size: 3rem;"></i>
        <h5 class="text-muted mt-3">未找到专利信息</h5>
        <p class="text-muted">请检查专利ID是否正确</p>
      </div>

      <!-- Success Message -->
      <div v-if="successMessage" class="alert alert-success alert-dismissible fade show mt-3" role="alert">
        <i class="bi bi-check-circle me-2"></i>
        {{ successMessage }}
        <button type="button" class="btn-close" @click="successMessage = null"></button>
      </div>
    </div>

    <!-- User Detail Modal -->
    <UserDetailModal
      modal-id="userDetailModal"
      :user-address="selectedUserAddress"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { patentService } from '@/services/patentService'
import { transactionService } from '@/services/transactionService'
import UserDetailModal from '@/components/UserDetailModal.vue'
import { Modal } from 'bootstrap'

/**
 * Simplified Patent Transaction Flow:
 * 1. User initiates purchase → Status: 'pending_transaction' (waiting for reviewer approval)
 * 2. Reviewer approves transaction → Automatically completes ownership transfer → Status: 'transaction_completed'
 * 
 * Note: Approval now automatically triggers ownership transfer in a single step.
 * No intermediate "approved" status - transactions go directly from pending to completed.
 */

export default {
  name: 'PatentDetailView',
  components: {
    UserDetailModal
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()

    const isLoading = ref(false)
    const isTransacting = ref(false)
    const error = ref(null)
    const successMessage = ref(null)
    const patent = ref(null)
    const selectedUserAddress = ref(null)

    const canTrade = computed(() => {
      return patent.value &&
             authStore.isConnected &&
             (patent.value.status === 'normal' || patent.value.status === 'approved') &&
             patent.value.uploaderAddress !== authStore.account &&
             patent.value.status !== 'trading' &&
             patent.value.status !== 'pending' &&
             patent.value.status !== 'pending_transaction' &&
             patent.value.status !== 'transaction_completed'
    })

    const loadPatentDetails = async () => {
      try {
        isLoading.value = true
        error.value = null

        const patentId = route.params.id
        console.log('🔍 Loading patent details for ID:', patentId)
        const details = await patentService.getPatentDetails(patentId)
        console.log('🔍 Received patent details:', details)

        // Ensure the patent object has proper structure with fallbacks
        patent.value = {
          id: details.id,
          name: details.name || 'N/A',
          number: details.number || 'N/A',
          category: details.category || '未分类',
          price: details.price || 0,
          applicationDate: details.applicationDate,
          expiryDate: details.expiryDate,
          ownerName: details.ownerName || 'N/A',
          uploaderAddress: details.uploaderAddress,
          uploaderName: details.uploaderName || 'N/A',
          uploaderPhone: details.uploaderPhone || 'N/A',
          abstract: details.abstract || '暂无摘要信息',
          status: details.status || 'unknown',
          isProxySale: details.isProxySale || false,
          uploadDate: details.uploadDate,
          viewCount: details.viewCount || 0,
          downloadCount: details.downloadCount || 0,
          blockchain: details.blockchain ? {
            transactionHash: details.blockchain.transactionHash,
            blockNumber: details.blockchain.blockNumber,
            contractAddress: details.blockchain.contractAddress
          } : null,
          transactionHistory: details.transactionHistory || []
        }

        // Check for pending transactions to update status
        await checkPendingTransactions()

      } catch (err) {
        error.value = err.message
      } finally {
        isLoading.value = false
      }
    }

    const checkPendingTransactions = async () => {
      try {
        // First check if current user has any transactions for this patent
        if (authStore.isConnected && authStore.account) {
          try {
            const userTransactions = await transactionService.getUserTransactions(authStore.account)
            const userPatentTransaction = userTransactions.find(
              transaction => transaction.patentId === patent.value.id &&
              (transaction.status === 'pending' || transaction.status === 'approved')
            )

            if (userPatentTransaction) {
              // Update patent status based on transaction status
              switch (userPatentTransaction.status) {
                case 'pending':
                  patent.value.status = 'pending_transaction'
                  patent.value.pendingTransaction = {
                    buyerAddress: userPatentTransaction.buyerAddress,
                    buyerName: userPatentTransaction.buyerName,
                    sellerAddress: userPatentTransaction.sellerAddress,
                    sellerName: userPatentTransaction.sellerName,
                    price: userPatentTransaction.price,
                    submitDate: userPatentTransaction.submitDate
                  }
                  break
                case 'completed':
                  patent.value.status = 'transaction_completed'
                  patent.value.pendingTransaction = {
                    buyerAddress: userPatentTransaction.buyerAddress,
                    buyerName: userPatentTransaction.buyerName,
                    sellerAddress: userPatentTransaction.sellerAddress,
                    sellerName: userPatentTransaction.sellerName,
                    price: userPatentTransaction.price,
                    submitDate: userPatentTransaction.submitDate
                  }
                  break
              }

              // Update transaction history with user's transaction
              if (!patent.value.transactionHistory.some(t => t.id === userPatentTransaction.id)) {
                patent.value.transactionHistory.unshift(userPatentTransaction)
              }
              return // Exit early if we found user's transaction
            }
          } catch (userTransactionError) {
            console.warn('Failed to load user transactions:', userTransactionError)
          }
        }

        // If no user transaction found, check for any pending transactions for this patent
        try {
          const pendingTransactions = await transactionService.getPendingTransactions()
          const patentTransaction = pendingTransactions.find(
            transaction => transaction.patentId === patent.value.id
          )

          if (patentTransaction) {
            // Only handle pending status here since getPendingTransactions only returns pending
            patent.value.status = 'pending_transaction'
            patent.value.pendingTransaction = {
              buyerAddress: patentTransaction.buyerAddress,
              buyerName: patentTransaction.buyerName,
              sellerAddress: patentTransaction.sellerAddress,
              sellerName: patentTransaction.sellerName,
              price: patentTransaction.price,
              submitDate: patentTransaction.submitDate
            }
          }
        } catch (pendingError) {
          console.warn('Failed to check pending transactions:', pendingError)
        }

      } catch (error) {
        console.warn('Failed to check transactions:', error)
        // Don't throw error here as this is supplementary information
      }
    }

    const initiateTransaction = async () => {
      try {
        isTransacting.value = true

        const transactionData = {
          patentId: patent.value.id,
          buyerAddress: authStore.account,
          sellerAddress: patent.value.uploaderAddress,
          price: patent.value.price
        }

        const result = await transactionService.initiateTransaction(transactionData)

        if (result.success) {
          successMessage.value = result.message
          // Update patent status to pending transaction
          patent.value.status = 'pending_transaction'
          // Refresh patent details to get updated transaction information
          await checkPendingTransactions()
        }

      } catch (err) {
        error.value = err.message
      } finally {
        isTransacting.value = false
      }
    }

    const initiateRightsProtection = () => {
      router.push({
        name: 'patent-protection',
        query: { patentId: patent.value.id }
      })
    }

    const showUserDetails = (address) => {
      selectedUserAddress.value = address
      const modal = new Modal(document.getElementById('userDetailModal'))
      modal.show()
    }

    const viewDocument = async (type) => {
      try {
        isLoading.value = true
        console.log(`📖 查看文档: ${type}`)

        const documentInfo = await patentService.viewPatentDocument(patent.value.id, type)

        if (documentInfo && documentInfo.url) {
          // Open document in new tab
          window.open(documentInfo.url, '_blank')
          successMessage.value = '文档已在新标签页中打开'

          // Clear success message after 3 seconds
          setTimeout(() => {
            successMessage.value = null
          }, 3000)
        } else {
          error.value = '无法获取文档URL'
        }
      } catch (err) {
        console.error('查看文档失败:', err)
        error.value = err.message || '查看文档失败'
      } finally {
        isLoading.value = false
      }
    }

    const downloadDocument = async (type) => {
      try {
        isLoading.value = true
        console.log(`💾 下载文档: ${type}`)

        await patentService.downloadPatentDocument(patent.value.id, type)

        successMessage.value = '文档下载成功'

        // Clear success message after 3 seconds
        setTimeout(() => {
          successMessage.value = null
        }, 3000)
      } catch (err) {
        console.error('下载文档失败:', err)
        error.value = err.message || '下载文档失败'
      } finally {
        isLoading.value = false
      }
    }

    const goBack = () => {
      router.go(-1)
    }

    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        successMessage.value = '已复制到剪贴板'
        setTimeout(() => {
          successMessage.value = null
        }, 2000)
      } catch (err) {
        console.error('复制失败:', err)
        error.value = '复制失败'
      }
    }

    // Utility functions
    const formatPrice = (price) => {
      if (price === null || price === undefined || isNaN(price)) return '0'
      return new Intl.NumberFormat('zh-CN').format(price)
    }

    const formatDate = (dateString) => {
      if (!dateString) return 'N/A'
      try {
        return new Date(dateString).toLocaleDateString('zh-CN')
      } catch (error) {
        return 'N/A'
      }
    }

    const formatAddress = (address) => {
      if (!address || typeof address !== 'string') {
        return 'N/A'
      }
      return `${address.slice(0, 6)}...${address.slice(-4)}`
    }

    const getStatusText = (status) => {
      const statusMap = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'normal': '正常',
        'withdrawn': '已撤回',
        'trading': '交易中',
        'under_review': '审核中',
        'under_protection': '维权中',
        'pending_transaction': '交易待审核',
        'transaction_completed': '交易已完成'
      }
      return statusMap[status] || '未知'
    }

    const getStatusBadgeClass = (status) => {
      const classMap = {
        'pending': 'bg-warning',
        'approved': 'bg-success',
        'rejected': 'bg-danger',
        'normal': 'bg-success',
        'withdrawn': 'bg-secondary',
        'trading': 'bg-info',
        'under_review': 'bg-info',
        'under_protection': 'bg-danger',
        'pending_transaction': 'bg-warning',
        'transaction_completed': 'bg-success'
      }
      return classMap[status] || 'bg-secondary'
    }

    const getUnavailableReason = (status) => {
      const reasonMap = {
        'trading': '该专利正在交易中',
        'pending': '该专利正在审核中',
        'under_review': '该专利正在审核中',
        'under_protection': '该专利正在维权中',
        'withdrawn': '该专利已被撤回',
        'rejected': '该专利审核未通过',
        'pending_transaction': '该专利有交易正在审核中',
        'transaction_completed': '该专利已被购买'
      }
      return reasonMap[status] || '该专利暂不可交易'
    }

    const checkTransactionStatus = async () => {
      try {
        isLoading.value = true
        console.log('🔄 检查交易状态')

        // Just refresh the patent details to get updated status
        await loadPatentDetails()
        successMessage.value = '状态已刷新'
      } catch (err) {
        console.error('检查交易状态失败:', err)
        error.value = err.message || '检查交易状态失败'
      } finally {
        isLoading.value = false
      }
    }

    const refreshPatentStatus = async () => {
      try {
        isLoading.value = true
        console.log('🔄 刷新专利状态')

        await loadPatentDetails()
      } catch (err) {
        console.error('刷新专利状态失败:', err)
        error.value = err.message || '刷新专利状态失败'
      } finally {
        isLoading.value = false
      }
    }

    const downloadContract = async (patentId) => {
      try {
        isLoading.value = true
        console.log('💾 下载转让证书')

        const contractInfo = await patentService.downloadPatentContract(patentId)

        if (contractInfo && contractInfo.url) {
          // Open contract in new tab
          window.open(contractInfo.url, '_blank')
          successMessage.value = '转让证书已在新标签页中打开'

          // Clear success message after 3 seconds
          setTimeout(() => {
            successMessage.value = null
          }, 3000)
        } else {
          error.value = '无法获取转让证书URL'
        }
      } catch (err) {
        console.error('下载转让证书失败:', err)
        error.value = err.message || '下载转让证书失败'
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      loadPatentDetails()
    })

    return {
      isLoading,
      isTransacting,
      error,
      successMessage,
      patent,
      selectedUserAddress,
      canTrade,
      initiateTransaction,
      initiateRightsProtection,
      viewDocument,
      downloadDocument,
      goBack,
      copyToClipboard,
      showUserDetails,
      formatPrice,
      formatDate,
      formatAddress,
      getStatusText,
      getStatusBadgeClass,
      getUnavailableReason,
      checkTransactionStatus,
      refreshPatentStatus,
      downloadContract
    }
  }
}
</script>

<style scoped>
.patent-detail-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 10px;
}

.card-header {
  border-radius: 10px 10px 0 0 !important;
}

.btn {
  border-radius: 6px;
}

.alert {
  border-radius: 8px;
}

.badge {
  border-radius: 6px;
}

.timeline {
  position: relative;
  padding-left: 2rem;
}

.timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
}

.timeline-item:not(:last-child)::before {
  content: '';
  position: absolute;
  left: -1.5rem;
  top: 1.5rem;
  width: 2px;
  height: calc(100% + 0.5rem);
  background-color: #dee2e6;
}

.timeline-marker {
  position: absolute;
  left: -1.75rem;
  top: 0.25rem;
  width: 0.5rem;
  height: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border-radius: 50%;
}

.timeline-content {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 3px solid #007bff;
}

code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-size: 0.875rem;
}
</style>
