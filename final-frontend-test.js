const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

/**
 * Final comprehensive test with all fixes applied
 * This test simulates the exact frontend behavior after all fixes
 */

async function createTestPDFs() {
  const pdfContent = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>endobj
xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
trailer<</Size 4/Root 1 0 R>>
startxref
174
%%EOF`;

  fs.writeFileSync('final-test-patent.pdf', pdfContent);
  fs.writeFileSync('final-test-ownership.pdf', pdfContent);
  console.log('✅ Final test PDF files created');
}

async function testFinalFrontendRequest() {
  console.log('🔍 Testing final frontend request with all fixes...');
  
  try {
    await createTestPDFs();
    
    // Create FormData exactly like the fixed frontend
    const formData = new FormData();
    
    // Form data with realistic values
    const testData = {
      patentName: '测试专利名称 - 最终验证',
      patentNumber: `CN${Date.now().toString().slice(-12)}.5`,
      patentCategory: 'invention',
      transferPrice: '25000',
      patentAbstract: '这是一个用于最终验证的测试专利摘要。该专利描述了一种创新的技术解决方案，具有重要的实用价值和商业前景。摘要内容详细描述了技术的核心特点和优势。',
      applicationDate: '2023-01-15',
      expirationDate: '2043-01-15',
      ownerName: '测试用户',
      ownerIdNumber: '110101199001011234',
      isAgentSale: false
    };
    
    // Add form data with proper type handling
    Object.entries(testData).forEach(([key, value]) => {
      if (key === 'isAgentSale') {
        // Apply the boolean fix
        formData.append(key, value.toString());
      } else {
        formData.append(key, value);
      }
    });
    
    formData.append('uploaderAddress', '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252');
    
    // Add files
    const patentFile = fs.createReadStream('final-test-patent.pdf');
    const ownershipFile = fs.createReadStream('final-test-ownership.pdf');
    
    formData.append('patentDocument', patentFile, {
      filename: 'final-test-patent.pdf',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', ownershipFile, {
      filename: 'final-test-ownership.pdf',
      contentType: 'application/pdf'
    });
    
    console.log('📋 Final test data prepared:');
    console.log('   ✅ Boolean fix applied: isAgentSale =', testData.isAgentSale.toString());
    console.log('   ✅ Proper file types: PDF documents');
    console.log('   ✅ Valid form data: All fields properly formatted');
    
    // Make request without manually setting Content-Type (let axios handle it)
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        // Don't set Content-Type manually - let axios set it with boundary
        'X-User-Address': '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252',
        'User-Agent': 'Mozilla/5.0 (Frontend Test)',
        'Accept': 'application/json',
        'Origin': 'http://localhost:5174'
      },
      timeout: 30000
    });
    
    console.log('✅ FINAL TEST SUCCESSFUL!');
    console.log('Response:', {
      success: response.data.success,
      patentId: response.data.data?.patentId,
      status: response.data.data?.status,
      message: response.data.message,
      transactionHash: response.data.data?.blockchain?.transactionHash
    });
    
    return { success: true, data: response.data };
    
  } catch (error) {
    console.log('❌ FINAL TEST FAILED:');
    console.log('Status:', error.response?.status);
    console.log('Error Code:', error.response?.data?.error?.code);
    console.log('Error Message:', error.response?.data?.error?.message);
    
    if (error.response?.status === 400) {
      console.log('\n🔍 Detailed error analysis:');
      if (error.response.data.error?.details?.errors) {
        error.response.data.error.details.errors.forEach((err, index) => {
          console.log(`   ${index + 1}. Field: ${err.path || err.param}`);
          console.log(`      Error: ${err.msg || err.message}`);
          console.log(`      Value: ${JSON.stringify(err.value)}`);
        });
      }
    }
    
    return { success: false, error: error.response?.data || error.message };
  } finally {
    // Clean up
    try {
      fs.unlinkSync('final-test-patent.pdf');
      fs.unlinkSync('final-test-ownership.pdf');
      console.log('🧹 Test files cleaned up');
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

async function testWithEmptyFields() {
  console.log('\n🔍 Testing validation with empty fields...');
  
  try {
    const formData = new FormData();
    
    // Add empty/invalid data to trigger validation
    formData.append('patentName', '');
    formData.append('patentNumber', '');
    formData.append('patentCategory', '');
    formData.append('transferPrice', '');
    formData.append('patentAbstract', '');
    formData.append('applicationDate', '');
    formData.append('expirationDate', '');
    formData.append('ownerName', '');
    formData.append('ownerIdNumber', '');
    formData.append('isAgentSale', 'false');
    formData.append('uploaderAddress', '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252');
    
    // Add empty files
    formData.append('patentDocument', Buffer.from(''), {
      filename: '',
      contentType: 'application/pdf'
    });
    formData.append('ownershipDocument', Buffer.from(''), {
      filename: '',
      contentType: 'application/pdf'
    });
    
    const response = await axios.post('http://localhost:3000/api/patents/upload', formData, {
      headers: {
        'X-User-Address': '0xd4db6FdBFFcaFDc8a6e43dA099c2C17344069252'
      },
      timeout: 10000
    });
    
    console.log('❌ Expected validation error but got success');
    
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Validation working correctly - got expected error for empty fields');
      const errorCount = error.response.data.error?.details?.errors?.length || 0;
      console.log(`   Found ${errorCount} validation errors`);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
}

// Run final comprehensive test
async function runFinalTest() {
  console.log('🚀 RUNNING FINAL COMPREHENSIVE TEST');
  console.log('=====================================\n');
  
  const result = await testFinalFrontendRequest();
  await testWithEmptyFields();
  
  console.log('\n📊 FINAL TEST SUMMARY:');
  console.log('======================');
  
  if (result.success) {
    console.log('✅ ALL FIXES SUCCESSFUL!');
    console.log('✅ Patent upload is now working correctly');
    console.log('✅ Frontend application should work properly');
    console.log('\n🎯 FIXES APPLIED:');
    console.log('   1. ✅ Boolean conversion fix: form.isAgentSale.toString()');
    console.log('   2. ✅ Content-Type header fix: Let axios handle multipart boundary');
    console.log('   3. ✅ File type validation: Using proper PDF files');
    console.log('   4. ✅ Form data validation: All fields properly formatted');
  } else {
    console.log('❌ ISSUES STILL REMAIN:');
    console.log('❌ Additional debugging may be needed');
    console.log('Error details:', result.error);
  }
  
  console.log('\n✅ Final test completed');
}

runFinalTest().catch(console.error);
