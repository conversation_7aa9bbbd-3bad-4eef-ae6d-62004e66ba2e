// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./UserManagement.sol";

/**
 * @title PatentRegistry
 * @dev Smart contract for managing patent registration and metadata
 */
contract PatentRegistry {

    // Patent status enum
    enum PatentStatus { PENDING, APPROVED, REJECTED, WITHDRAWN, NORMAL, TRADING }

    // Patent structure
    struct Patent {
        uint256 id;
        string name;
        string number;
        string category;
        uint256 price;
        string abstractText;
        uint256 applicationDate;
        uint256 expirationDate;
        string ownerName;
        string ownerIdNumber;
        address uploaderAddress;      // Original uploader (never changes)
        address currentOwnerAddress;  // Current owner (changes with transfers)
        bool isAgentSale;
        PatentStatus status;
        string documentHash; // IPFS hash for patent document
        string ownershipDocumentHash; // IPFS hash for ownership document
        uint256 uploadDate;
        uint256 viewCount;
        uint256 downloadCount;
        string blockchainTxHash;
        uint256 blockNumber;
        address reviewedBy;
        string reviewComments;
        uint256 reviewDate;
    }

    // Events
    event PatentUploaded(uint256 indexed patentId, address indexed uploader, string name);
    event PatentApproved(uint256 indexed patentId, address indexed reviewer);
    event PatentRejected(uint256 indexed patentId, address indexed reviewer, string reason);
    event PatentWithdrawn(uint256 indexed patentId, address indexed owner, string reason);
    event PatentRestored(uint256 indexed patentId, address indexed owner);
    event PatentViewed(uint256 indexed patentId, address indexed viewer);
    event PatentDownloaded(uint256 indexed patentId, address indexed downloader);
    event PatentOwnershipTransferred(uint256 indexed patentId, address indexed previousOwner, address indexed newOwner);

    // State variables
    mapping(uint256 => Patent) public patents;
    mapping(address => uint256[]) public userPatents;
    mapping(string => bool) public patentNumbers; // To ensure unique patent numbers
    uint256 public nextPatentId;
    uint256 public totalPatents;

    UserManagement public userManagement;
    address public protectionManagerAddress;
    address public owner;

    // Modifiers
    modifier onlyReviewerOrAdmin() {
        UserManagement.Role role = userManagement.getUserRole(msg.sender);
        require(
            role == UserManagement.Role.REVIEWER ||
            role == UserManagement.Role.ADMIN,
            "Only reviewer or admin can perform this action"
        );
        _;
    }

    modifier patentExists(uint256 patentId) {
        require(patentId < nextPatentId && patents[patentId].id == patentId, "Patent does not exist");
        _;
    }

    modifier onlyPatentOwner(uint256 patentId) {
        require(patents[patentId].currentOwnerAddress == msg.sender, "Only patent owner can perform this action");
        _;
    }

    constructor(address _userManagementAddress, address _protectionManagerAddress) {
        userManagement = UserManagement(_userManagementAddress);
        protectionManagerAddress = _protectionManagerAddress;
        owner = msg.sender;
        nextPatentId = 1;
        totalPatents = 0;
    }

    /**
     * @dev Upload a new patent for review
     */
    function uploadPatent(
        string memory name,
        string memory number,
        string memory category,
        uint256 price,
        string memory abstractText,
        uint256 applicationDate,
        uint256 expirationDate,
        string memory ownerName,
        string memory ownerIdNumber,
        bool isAgentSale,
        string memory documentHash,
        string memory ownershipDocumentHash
    ) external returns (uint256) {
        require(bytes(name).length > 0, "Patent name cannot be empty");
        require(bytes(number).length > 0, "Patent number cannot be empty");
        require(!patentNumbers[number], "Patent number already exists");
        require(price > 0, "Price must be greater than 0");
        require(applicationDate < expirationDate, "Invalid date range");
        require(bytes(documentHash).length > 0, "Document hash cannot be empty");
        require(userManagement.isUserActive(msg.sender), "User is not active");

        uint256 patentId = nextPatentId;
        nextPatentId++;

        patents[patentId] = Patent({
            id: patentId,
            name: name,
            number: number,
            category: category,
            price: price,
            abstractText: abstractText,
            applicationDate: applicationDate,
            expirationDate: expirationDate,
            ownerName: ownerName,
            ownerIdNumber: ownerIdNumber,
            uploaderAddress: msg.sender,
            currentOwnerAddress: msg.sender,  // Initially same as uploader
            isAgentSale: isAgentSale,
            status: PatentStatus.PENDING,
            documentHash: documentHash,
            ownershipDocumentHash: ownershipDocumentHash,
            uploadDate: block.timestamp,
            viewCount: 0,
            downloadCount: 0,
            blockchainTxHash: "",
            blockNumber: block.number,
            reviewedBy: address(0),
            reviewComments: "",
            reviewDate: 0
        });

        userPatents[msg.sender].push(patentId);
        patentNumbers[number] = true;
        totalPatents++;

        emit PatentUploaded(patentId, msg.sender, name);

        return patentId;
    }

    /**
     * @dev Approve a patent (reviewer/admin only)
     */
    function approvePatent(uint256 patentId, string memory comments)
        external
        onlyReviewerOrAdmin
        patentExists(patentId)
    {
        require(patents[patentId].status == PatentStatus.PENDING, "Patent is not pending review");

        patents[patentId].status = PatentStatus.APPROVED;
        patents[patentId].reviewedBy = msg.sender;
        patents[patentId].reviewComments = comments;
        patents[patentId].reviewDate = block.timestamp;

        emit PatentApproved(patentId, msg.sender);
    }

    /**
     * @dev Reject a patent (reviewer/admin only)
     */
    function rejectPatent(uint256 patentId, string memory reason)
        external
        onlyReviewerOrAdmin
        patentExists(patentId)
    {
        require(patents[patentId].status == PatentStatus.PENDING, "Patent is not pending review");

        patents[patentId].status = PatentStatus.REJECTED;
        patents[patentId].reviewedBy = msg.sender;
        patents[patentId].reviewComments = reason;
        patents[patentId].reviewDate = block.timestamp;

        emit PatentRejected(patentId, msg.sender, reason);
    }

    /**
     * @dev Withdraw patent from trading
     */
    function withdrawPatent(uint256 patentId, string memory reason)
        external
        onlyPatentOwner(patentId)
        patentExists(patentId)
    {
        require(
            patents[patentId].status == PatentStatus.APPROVED ||
            patents[patentId].status == PatentStatus.NORMAL,
            "Patent cannot be withdrawn"
        );

        patents[patentId].status = PatentStatus.WITHDRAWN;

        emit PatentWithdrawn(patentId, msg.sender, reason);
    }

    /**
     * @dev Restore patent to trading
     */
    function restorePatent(uint256 patentId)
        external
        onlyPatentOwner(patentId)
        patentExists(patentId)
    {
        require(patents[patentId].status == PatentStatus.WITHDRAWN, "Patent is not withdrawn");

        patents[patentId].status = PatentStatus.NORMAL;

        emit PatentRestored(patentId, msg.sender);
    }

    /**
     * @dev Increment view count
     */
    function incrementViewCount(uint256 patentId) external patentExists(patentId) {
        patents[patentId].viewCount++;
        emit PatentViewed(patentId, msg.sender);
    }

    /**
     * @dev Increment download count
     */
    function incrementDownloadCount(uint256 patentId) external patentExists(patentId) {
        patents[patentId].downloadCount++;
        emit PatentDownloaded(patentId, msg.sender);
    }

    /**
     * @dev Get patent details
     */
    function getPatent(uint256 patentId) external view patentExists(patentId) returns (Patent memory) {
        return patents[patentId];
    }

    /**
     * @dev Get patents by current owner (what user currently owns)
     */
    function getUserPatents(address userAddress) external view returns (uint256[] memory) {
        return userPatents[userAddress];
    }

    /**
     * @dev Get patents originally uploaded by user (regardless of current ownership)
     */
    function getUserUploadedPatents(address userAddress) external view returns (uint256[] memory) {
        uint256[] memory uploadedPatents = new uint256[](totalPatents);
        uint256 count = 0;

        for (uint256 i = 1; i < nextPatentId; i++) {
            if (patents[i].uploaderAddress == userAddress) {
                uploadedPatents[count] = i;
                count++;
            }
        }

        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = uploadedPatents[i];
        }

        return result;
    }

    /**
     * @dev Get pending patents for review
     * Note: Removed access control since this is a view function that doesn't change state
     * Access control should be handled at the API level
     */
    function getPendingPatents() external view returns (uint256[] memory) {
        uint256[] memory pendingPatents = new uint256[](totalPatents);
        uint256 count = 0;

        for (uint256 i = 1; i < nextPatentId; i++) {
            if (patents[i].status == PatentStatus.PENDING) {
                pendingPatents[count] = i;
                count++;
            }
        }

        // Resize array to actual count
        uint256[] memory result = new uint256[](count);
        for (uint256 i = 0; i < count; i++) {
            result[i] = pendingPatents[i];
        }

        return result;
    }

    /**
     * @dev Get total patents count
     */
    function getTotalPatents() external view returns (uint256) {
        return totalPatents;
    }

    /**
     * @dev Check if patent number exists
     */
    function patentNumberExists(string memory number) external view returns (bool) {
        return patentNumbers[number];
    }

    /**
     * @dev Set patent trading status (used during transaction process)
     */
    function setTradingStatus(uint256 patentId, bool isTrading)
        external
        patentExists(patentId)
    {
        // Allow current owner or reviewer/admin to set trading status
        require(
            msg.sender == patents[patentId].currentOwnerAddress ||
            userManagement.getUserRole(msg.sender) == UserManagement.Role.REVIEWER ||
            userManagement.getUserRole(msg.sender) == UserManagement.Role.ADMIN,
            "Not authorized to change trading status"
        );

        if (isTrading) {
            require(
                patents[patentId].status == PatentStatus.APPROVED ||
                patents[patentId].status == PatentStatus.NORMAL,
                "Patent must be approved or normal to start trading"
            );
            patents[patentId].status = PatentStatus.TRADING;
        } else {
            require(
                patents[patentId].status == PatentStatus.TRADING,
                "Patent must be in trading status to clear"
            );
            patents[patentId].status = PatentStatus.NORMAL;
        }
    }

    /**
     * @dev Set ProtectionManager address (owner only - used during deployment)
     */
    function setProtectionManagerAddress(address _protectionManagerAddress) external {
        require(msg.sender == owner, "Only owner can set ProtectionManager address");
        require(_protectionManagerAddress != address(0), "ProtectionManager address cannot be zero");
        protectionManagerAddress = _protectionManagerAddress;
    }

    /**
     * @dev Transfer patent ownership (reviewer/admin or ProtectionManager only)
     */
    function transferOwnership(uint256 patentId, address newOwner)
        external
        patentExists(patentId)
    {
        // Allow reviewers/admins or the ProtectionManager contract to transfer ownership
        bool isAuthorized =
            userManagement.getUserRole(msg.sender) == UserManagement.Role.REVIEWER ||
            userManagement.getUserRole(msg.sender) == UserManagement.Role.ADMIN ||
            msg.sender == protectionManagerAddress;

        require(isAuthorized, "Not authorized to transfer patent ownership");
        require(newOwner != address(0), "New owner cannot be zero address");
        require(newOwner != patents[patentId].currentOwnerAddress, "New owner cannot be the same as current owner");
        require(userManagement.isUserActive(newOwner), "New owner is not active");

        address previousOwner = patents[patentId].currentOwnerAddress;

        // Remove patent from previous owner's list
        uint256[] storage prevOwnerPatents = userPatents[previousOwner];
        for (uint256 i = 0; i < prevOwnerPatents.length; i++) {
            if (prevOwnerPatents[i] == patentId) {
                // Replace with last element and pop
                prevOwnerPatents[i] = prevOwnerPatents[prevOwnerPatents.length - 1];
                prevOwnerPatents.pop();
                break;
            }
        }

        // Add patent to new owner's list
        userPatents[newOwner].push(patentId);

        // Update patent's current owner address (keep original uploader unchanged)
        patents[patentId].currentOwnerAddress = newOwner;

        // Set status to normal after ownership transfer
        patents[patentId].status = PatentStatus.NORMAL;

        emit PatentOwnershipTransferred(patentId, previousOwner, newOwner);
    }
}
