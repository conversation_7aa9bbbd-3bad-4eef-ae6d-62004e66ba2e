const express = require('express');
const multer = require('multer');
const router = express.Router();

// Import middleware
const {
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  requireReviewerOrAdmin,
  validateAddressParam,
  requireOwnershipOrAdmin,
  optionalAuth
} = require('../middleware/authMiddleware');
const { validationRules, handleValidationErrors } = require('../middleware/validationMiddleware');
const { asyncHandler } = require('../middleware/errorHandler');

// Import controllers
const patentController = require('../controllers/patentController');

// Configure multer for patent file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024, // 10MB default
    files: 2 // Maximum 2 files per request (patent document + ownership document)
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'pdf,doc,docx,jpg,jpeg,png').split(',');
    const fileExtension = file.originalname.split('.').pop().toLowerCase();

    if (allowedTypes.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new Error(`File type .${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
    }
  }
});

/**
 * @route POST /api/patents/upload
 * @desc Upload a new patent for review
 * @access Authenticated users
 */
router.post('/upload',
  extractUserAddress,
  verifyActiveUser,
  upload.fields([
    { name: 'patentDocument', maxCount: 1 },
    { name: 'ownershipDocument', maxCount: 1 }
  ]),
  validationRules.patentUpload(),
  handleValidationErrors,
  asyncHandler(patentController.uploadPatent)
);

/**
 * @route GET /api/patents/search
 * @desc Search patents with filters
 * @access Public
 */
router.get('/search',
  optionalAuth,
  validationRules.patentSearch(),
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(patentController.searchPatents)
);

/**
 * @route GET /api/patents/user/:address
 * @desc Get patents originally uploaded by a specific user
 * @access Public
 */
router.get('/user/:address',
  validateAddressParam('address'),
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(patentController.getUserPatents)
);

/**
 * @route GET /api/patents/owned/:address
 * @desc Get patents currently owned by a specific user
 * @access Public
 */
router.get('/owned/:address',
  validateAddressParam('address'),
  validationRules.pagination(),
  handleValidationErrors,
  asyncHandler(patentController.getUserOwnedPatents)
);

/**
 * @route GET /api/patents/:id/download/:documentType
 * @desc Download patent document
 * @access Public
 */
router.get('/:id/download/:documentType',
  optionalAuth,
  asyncHandler(patentController.downloadPatentDocument)
);

/**
 * @route GET /api/patents/:id/view/:documentType
 * @desc Get patent document URL for viewing
 * @access Public
 */
router.get('/:id/view/:documentType',
  optionalAuth,
  asyncHandler(patentController.viewPatentDocument)
);

/**
 * @route POST /api/patents/:id/view
 * @desc Increment patent view count
 * @access Public
 */
router.post('/:id/view',
  asyncHandler(patentController.incrementViewCount)
);

/**
 * @route PUT /api/patents/:id/withdraw
 * @desc Withdraw patent from trading (make unavailable)
 * @access Patent owner
 */
router.put('/:id/withdraw',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(patentController.withdrawPatent)
);

/**
 * @route PUT /api/patents/:id/restore
 * @desc Restore patent to trading (make available again)
 * @access Patent owner
 */
router.put('/:id/restore',
  extractUserAddress,
  verifyActiveUser,
  getUserRole,
  asyncHandler(patentController.restorePatent)
);

/**
 * @route GET /api/patents/:id
 * @desc Get detailed patent information
 * @access Public
 */
router.get('/:id',
  optionalAuth,
  asyncHandler(patentController.getPatentDetails)
);

module.exports = router;
