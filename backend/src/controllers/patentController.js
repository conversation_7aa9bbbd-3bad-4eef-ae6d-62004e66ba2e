const blockchainService = require('../services/blockchainService');
const ipfsService = require('../services/ipfsService');
const { formatDate, formatAddress, parsePaginationParams, createPagination, formatBlockchainData } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError, BlockchainError, IPFSError } = require('../middleware/errorHandler');

/**
 * Patent Controller
 * Handles patent management operations
 */

/**
 * Upload a new patent for review
 */
const uploadPatent = async (req, res) => {
  try {
    // Extract form data
    const {
      patentName,
      patentNumber,
      patentCategory,
      transferPrice,
      patentAbstract,
      applicationDate,
      expirationDate,
      ownerName,
      ownerIdNumber,
      isAgentSale,
      uploaderAddress
    } = req.body;

    // Get uploader address from middleware (fallback to form data if available)
    const finalUploaderAddress = req.userAddress || uploaderAddress;

    // Validate required files
    if (!req.files || !req.files.patentDocument || !req.files.ownershipDocument) {
      throw new ValidationError('Both patent document and ownership document are required');
    }

    const patentDocumentFile = req.files.patentDocument[0];
    const ownershipDocumentFile = req.files.ownershipDocument[0];

    console.log('📄 Uploading patent:', {
      name: patentName,
      number: patentNumber,
      uploader: finalUploaderAddress,
      patentDocSize: patentDocumentFile.size,
      ownershipDocSize: ownershipDocumentFile.size
    });

    // Upload files to IPFS
    console.log('📁 Uploading files to IPFS...');

    const patentDocResult = await ipfsService.uploadFile(
      patentDocumentFile.buffer,
      patentDocumentFile.originalname
    );

    const ownershipDocResult = await ipfsService.uploadFile(
      ownershipDocumentFile.buffer,
      ownershipDocumentFile.originalname
    );

    // Pin files to ensure they stay available
    await Promise.all([
      ipfsService.pinFile(patentDocResult.hash).catch(error => {
        console.warn('Failed to pin patent document:', error);
      }),
      ipfsService.pinFile(ownershipDocResult.hash).catch(error => {
        console.warn('Failed to pin ownership document:', error);
      })
    ]);

    console.log('✅ Files uploaded to IPFS:', {
      patentDoc: patentDocResult.hash,
      ownershipDoc: ownershipDocResult.hash
    });

    // Convert dates to Unix timestamps
    const applicationTimestamp = Math.floor(new Date(applicationDate).getTime() / 1000);
    const expirationTimestamp = Math.floor(new Date(expirationDate).getTime() / 1000);

    // Convert price to Wei (assuming price is in ETH)
    const priceInWei = blockchainService.web3.utils.toWei(transferPrice.toString(), 'ether');

    // Check if patent number already exists before uploading
    console.log('🔍 Checking if patent number already exists...');
    try {
      const patentNumberExists = await blockchainService.callContractMethod(
        'PatentRegistry',
        'patentNumbers',
        [patentNumber]
      );

      if (patentNumberExists) {
        throw new ValidationError('专利号已存在，请检查专利号是否正确');
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        throw error;
      }
      console.warn('Failed to check patent number existence:', error);
      // Continue with upload if check fails
    }

    // Upload patent to blockchain
    console.log('⛓️ Uploading patent to blockchain...');

    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'uploadPatent',
      [
        patentName,
        patentNumber,
        patentCategory,
        priceInWei,
        patentAbstract,
        applicationTimestamp,
        expirationTimestamp,
        ownerName,
        ownerIdNumber,
        isAgentSale === 'true' || isAgentSale === true,
        patentDocResult.hash,
        ownershipDocResult.hash
      ],
      {
        send: true,
        from: finalUploaderAddress
      }
    );

    console.log('✅ Patent uploaded to blockchain:', result.transactionHash);

    // Extract patent ID from transaction events
    let patentId = null;
    if (result.events && result.events.PatentUploaded) {
      patentId = result.events.PatentUploaded.returnValues.patentId;
    }

    // Prepare response
    const response = {
      patentId: patentId ? patentId.toString() : 'pending',
      status: 'under_review',
      documentHashes: {
        patent: patentDocResult.hash,
        ownership: ownershipDocResult.hash
      },
      uploadDate: new Date().toISOString(),
      blockchain: {
        transactionHash: result.transactionHash,
        blockNumber: result.blockNumber
      },
      message: '专利上传成功，等待审核'
    };

    res.created(response, '专利上传成功，等待审核');

  } catch (error) {
    console.error('Error uploading patent:', error);

    // Handle specific error types
    if (error.message.includes('IPFS')) {
      throw new IPFSError('文件上传到IPFS失败，请重试');
    } else if (error.message.includes('revert') || error.message.includes('gas')) {
      throw new BlockchainError('区块链交易失败，请检查网络连接和账户余额');
    } else if (error.message.includes('Patent number already exists')) {
      throw new ValidationError('专利号已存在，请检查专利号是否正确');
    }

    throw error;
  }
};

/**
 * Search patents with filters
 */
const searchPatents = async (req, res) => {
  try {
    const {
      name,
      number,
      category,
      minPrice,
      maxPrice,
      status,
      page = 1,
      limit = 20,
      sortBy = 'uploadDate',
      sortOrder = 'desc'
    } = req.query;

    // Parse pagination parameters
    const { page: parsedPage, limit: parsedLimit, offset } = parsePaginationParams(req);
    const pageSize = parsedLimit;

    // Get total patent count first
    const totalPatents = await blockchainService.callContractMethod(
      'PatentRegistry',
      'totalPatents'
    );

    const patents = [];
    const searchResults = [];

    // Get all patents and filter them
    for (let i = 1; i <= totalPatents; i++) {
      try {
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [i]
        );

        // Apply filters
        let includePatent = true;

        // ID filter (exact match) - if the search term is a number, also check ID
        const isNumericSearch = !isNaN(parseInt(name)) || !isNaN(parseInt(number));
        if (isNumericSearch) {
          const searchId = parseInt(name) || parseInt(number);
          if (searchId && parseInt(patent.id) === searchId) {
            includePatent = true;
          } else {
            // Continue with other filters
          }
        }

        // Name filter (case-insensitive partial match)
        if (name && !patent.name.toLowerCase().includes(name.toLowerCase())) {
          // Don't exclude if this was already matched by ID
          if (!isNumericSearch || parseInt(patent.id) !== parseInt(name)) {
            includePatent = false;
          }
        }

        // Patent number filter (partial match)
        if (number && !patent.number.toLowerCase().includes(number.toLowerCase())) {
          // Don't exclude if this was already matched by ID
          if (!isNumericSearch || parseInt(patent.id) !== parseInt(number)) {
            includePatent = false;
          }
        }

        // Category filter (case-insensitive partial match)
        if (category && !patent.category.toLowerCase().includes(category.toLowerCase())) {
          includePatent = false;
        }

        // Price range filter
        const patentPriceInEth = parseFloat(blockchainService.web3.utils.fromWei(patent.price, 'ether'));
        if (minPrice && patentPriceInEth < parseFloat(minPrice)) {
          includePatent = false;
        }
        if (maxPrice && patentPriceInEth > parseFloat(maxPrice)) {
          includePatent = false;
        }

        // Status filter
        if (status) {
          const statusMap = {
            'pending': 0,
            'approved': 1,
            'rejected': 2,
            'normal': 3,
            'withdrawn': 4
          };
          if (statusMap[status.toLowerCase()] !== undefined &&
              patent.status !== statusMap[status.toLowerCase()]) {
            includePatent = false;
          }
        }

        // Only include approved or normal patents for regular users
        // Anonymous users (no role) and regular users can only see approved/normal patents
        if ((!req.userRole || req.userRole === 'user') && parseInt(patent.status) !== 1 && parseInt(patent.status) !== 3) {
          includePatent = false;
        }

        if (includePatent) {
          // Get uploader profile
          let uploaderName = 'Unknown';
          try {
            // First check if user is registered
            const isRegistered = await blockchainService.callContractMethod(
              'UserManagement',
              'registeredUsers',
              [patent.uploaderAddress]
            );

            if (isRegistered) {
              const uploaderProfile = await blockchainService.callContractMethod(
                'UserManagement',
                'getUserProfile',
                [patent.uploaderAddress]
              );
              uploaderName = uploaderProfile.name || 'Unknown';
            }
          } catch (error) {
            console.warn('Failed to get uploader profile:', error);
          }

          const statusNames = ['pending', 'approved', 'rejected', 'withdrawn', 'normal', 'trading'];

          searchResults.push({
            id: patent.id.toString(),
            name: patent.name,
            number: patent.number,
            category: patent.category,
            price: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
            status: statusNames[patent.status] || 'unknown',
            uploaderAddress: formatAddress(patent.uploaderAddress),
            currentOwnerAddress: formatAddress(patent.currentOwnerAddress),
            uploaderName,
            uploadDate: formatDate(new Date(parseInt(patent.uploadDate) * 1000)),
            viewCount: parseInt(patent.viewCount),
            downloadCount: parseInt(patent.downloadCount),
            sortValue: patent[sortBy] || patent.uploadDate
          });
        }
      } catch (error) {
        console.warn(`Failed to get patent ${i}:`, error);
        continue;
      }
    }

    // Sort results
    searchResults.sort((a, b) => {
      const aVal = a.sortValue;
      const bVal = b.sortValue;

      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });

    // Apply pagination
    const paginatedResults = searchResults.slice(offset, offset + pageSize);
    const pagination = createPagination(parsedPage, pageSize, searchResults.length);

    res.success({
      patents: paginatedResults,
      pagination
    });

  } catch (error) {
    console.error('Error searching patents:', error);
    throw error;
  }
};

/**
 * Get detailed patent information
 */
const getPatentDetails = async (req, res) => {
  try {
    const { id } = req.params;
    const patentId = parseInt(id);

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Get patent from blockchain
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if user has permission to view this patent
    // Logic:
    // - Admin/reviewer can view all patents
    // - Users can view their own patents (any status) and others' approved/normal patents
    // - Unauthenticated users can only view approved/normal patents
    if (req.userRole !== 'admin' && req.userRole !== 'reviewer') {
      const isOriginalUploader = req.userAddress && req.userAddress.toLowerCase() === patent.uploaderAddress.toLowerCase();
      const isCurrentOwner = req.userAddress && req.userAddress.toLowerCase() === patent.currentOwnerAddress.toLowerCase();

      if (!isOriginalUploader && !isCurrentOwner && (parseInt(patent.status) !== 1 && parseInt(patent.status) !== 4)) { // 1=approved, 4=normal
        throw new AuthorizationError('You do not have permission to view this patent');
      }
    }

    // Get uploader profile
    let uploaderProfile = { name: 'Unknown', phone: 'Unknown' };
    try {
      // First check if user is registered
      const isRegistered = await blockchainService.callContractMethod(
        'UserManagement',
        'registeredUsers',
        [patent.uploaderAddress]
      );

      if (isRegistered) {
        uploaderProfile = await blockchainService.callContractMethod(
          'UserManagement',
          'getUserProfile',
          [patent.uploaderAddress]
        );
      }
    } catch (error) {
      console.warn('Failed to get uploader profile:', error);
    }

    // Increment view count if this is a regular view (not admin/reviewer)
    if (req.userRole === 'user' && req.userAddress) {
      try {
        await blockchainService.callContractMethod(
          'PatentRegistry',
          'incrementViewCount',
          [patentId],
          { send: true, from: req.userAddress }
        );
      } catch (error) {
        console.warn('Failed to increment view count:', error);
      }
    }

    const statusNames = ['pending', 'approved', 'rejected', 'withdrawn', 'normal', 'trading'];

    // Get current owner profile
    let currentOwnerProfile = { name: 'Unknown', phone: 'Unknown' };
    try {
      if (patent.currentOwnerAddress !== patent.uploaderAddress) {
        const isRegistered = await blockchainService.callContractMethod(
          'UserManagement',
          'registeredUsers',
          [patent.currentOwnerAddress]
        );

        if (isRegistered) {
          currentOwnerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [patent.currentOwnerAddress]
          );
        }
      } else {
        currentOwnerProfile = uploaderProfile;
      }
    } catch (error) {
      console.warn('Failed to get current owner profile:', error);
    }

    // Prepare detailed response
    const patentDetails = {
      id: patent.id.toString(),
      name: patent.name,
      number: patent.number,
      category: patent.category,
      price: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
      transferPrice: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
      abstract: patent.abstractText,
      applicationDate: formatDate(new Date(parseInt(patent.applicationDate) * 1000)),
      expiryDate: formatDate(new Date(parseInt(patent.expirationDate) * 1000)),
      ownerName: patent.ownerName,
      ownerIdNumber: patent.ownerIdNumber,
      uploaderAddress: formatAddress(patent.uploaderAddress),
      currentOwnerAddress: formatAddress(patent.currentOwnerAddress),
      uploaderName: uploaderProfile.name || 'Unknown',
      uploaderPhone: uploaderProfile.phone || 'Unknown',
      currentOwnerName: currentOwnerProfile.name || 'Unknown',
      currentOwnerPhone: currentOwnerProfile.phone || 'Unknown',
      isProxySale: patent.isAgentSale,
      status: statusNames[parseInt(patent.status)] || 'unknown',
      documentHash: patent.documentHash,
      certificateHash: patent.ownershipDocumentHash,
      uploadDate: formatDate(new Date(parseInt(patent.uploadDate) * 1000)),
      viewCount: parseInt(patent.viewCount),
      downloadCount: parseInt(patent.downloadCount),
      blockchain: {
        transactionHash: patent.blockchainTxHash || 'N/A',
        blockNumber: patent.blockNumber.toString(),
        contractAddress: blockchainService.contractAddresses.PatentRegistry
      },
      transactionHistory: []
    };

    // Get patent transaction history (only completed transactions)
    try {
      const patentTransactionIds = await blockchainService.callContractMethod(
        'TransactionManager',
        'getPatentTransactions',
        [patentId]
      );

      const transactionHistory = [];

      for (const txId of patentTransactionIds) {
        try {
          const transaction = await blockchainService.callContractMethod(
            'TransactionManager',
            'getTransaction',
            [txId]
          );

          // Only include completed transactions in history
          if (parseInt(transaction.status) === 3) { // 3 = COMPLETED
            let buyerName = 'Unknown';
            let sellerName = 'Unknown';

            try {
              const buyerProfile = await blockchainService.callContractMethod(
                'UserManagement',
                'getUserProfile',
                [transaction.buyerAddress]
              );
              buyerName = buyerProfile.name || 'Unknown';
            } catch (error) {
              console.warn('Failed to get buyer profile for transaction history:', error);
            }

            try {
              const sellerProfile = await blockchainService.callContractMethod(
                'UserManagement',
                'getUserProfile',
                [transaction.sellerAddress]
              );
              sellerName = sellerProfile.name || 'Unknown';
            } catch (error) {
              console.warn('Failed to get seller profile for transaction history:', error);
            }

            transactionHistory.push({
              id: transaction.id.toString(),
              description: `专利转让: ${sellerName} → ${buyerName}`,
              price: blockchainService.web3.utils.fromWei(transaction.price, 'ether'),
              buyerAddress: formatAddress(transaction.buyerAddress),
              buyerName,
              sellerAddress: formatAddress(transaction.sellerAddress),
              sellerName,
              timestamp: formatDate(new Date(parseInt(transaction.completionDate) * 1000)),
              transactionHash: transaction.blockchainTxHash || 'N/A',
              status: 'completed'
            });
          }
        } catch (txError) {
          console.warn(`Failed to get transaction details for ID ${txId}:`, txError);
          continue;
        }
      }

      // Sort by completion date (newest first)
      transactionHistory.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      patentDetails.transactionHistory = transactionHistory;

    } catch (historyError) {
      console.warn('Failed to get patent transaction history:', historyError);
      // Continue without transaction history if it fails
    }

    res.success({ data: patentDetails });

  } catch (error) {
    console.error('Error getting patent details:', error);
    throw error;
  }
};

/**
 * Get patents originally uploaded by a specific user (regardless of current ownership)
 */
const getUserPatents = async (req, res) => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization - user can view own patents or admin/reviewer can view any
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only view your own patents');
    }

    // Get user's originally uploaded patent IDs from blockchain
    const patentIds = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getUserUploadedPatents',
      [address]
    );

    const userPatents = [];

    // Get details for each patent
    for (const patentId of patentIds) {
      try {
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [patentId]
        );

        const statusNames = ['pending', 'approved', 'rejected', 'withdrawn', 'normal', 'trading'];

        userPatents.push({
          id: patent.id.toString(),
          name: patent.name,
          number: patent.number,
          status: statusNames[parseInt(patent.status)] || 'unknown',
          uploadDate: formatDate(new Date(parseInt(patent.uploadDate) * 1000)),
          price: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
          viewCount: parseInt(patent.viewCount),
          downloadCount: parseInt(patent.downloadCount),
          category: patent.category,
          abstract: patent.abstractText || '',
          isAgentSale: patent.isAgentSale,
          uploaderAddress: patent.uploaderAddress,
          currentOwnerAddress: patent.currentOwnerAddress
        });
      } catch (error) {
        console.warn(`Failed to get patent details for ID ${patentId}:`, error);
        continue;
      }
    }

    // Sort by upload date (newest first)
    userPatents.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));

    res.success(userPatents);

  } catch (error) {
    console.error('Error getting user patents:', error);
    throw error;
  }
};

/**
 * Get patents currently owned by a specific user
 */
const getUserOwnedPatents = async (req, res) => {
  try {
    const { address } = req.params;

    // Validate address format
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization - user can view own patents or admin/reviewer can view any
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only view your own patents');
    }

    // Get user's currently owned patent IDs from blockchain
    const patentIds = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getUserPatents',
      [address]
    );

    const ownedPatents = [];

    // Get details for each patent
    for (const patentId of patentIds) {
      try {
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [patentId]
        );

        const statusNames = ['pending', 'approved', 'rejected', 'withdrawn', 'normal', 'trading'];

        ownedPatents.push({
          id: patent.id.toString(),
          name: patent.name,
          number: patent.number,
          status: statusNames[parseInt(patent.status)] || 'unknown',
          uploadDate: formatDate(new Date(parseInt(patent.uploadDate) * 1000)),
          price: blockchainService.web3.utils.fromWei(patent.price, 'ether'),
          viewCount: parseInt(patent.viewCount),
          downloadCount: parseInt(patent.downloadCount),
          category: patent.category,
          abstract: patent.abstractText || '',
          isAgentSale: patent.isAgentSale,
          uploaderAddress: patent.uploaderAddress,
          currentOwnerAddress: patent.currentOwnerAddress
        });
      } catch (error) {
        console.warn(`Failed to get patent details for ID ${patentId}:`, error);
        continue;
      }
    }

    res.success(ownedPatents);

  } catch (error) {
    console.error('Error getting user owned patents:', error);
    throw error;
  }
};

/**
 * Withdraw patent from trading
 */
const withdrawPatent = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const patentId = parseInt(id);

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Get patent details
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check authorization - only current patent owner or admin can withdraw
    if (req.userRole !== 'admin' &&
        req.userAddress.toLowerCase() !== patent.currentOwnerAddress.toLowerCase()) {
      throw new AuthorizationError('You can only withdraw patents you currently own');
    }

    // Check if patent can be withdrawn (must be approved or normal)
    if (patent.status !== 1 && patent.status !== 3) { // 1=approved, 3=normal
      throw new ValidationError('Patent must be approved or normal to be withdrawn');
    }

    // Withdraw patent
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'withdrawPatent',
      [patentId, reason || 'Withdrawn by owner'],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: '专利撤回成功'
    }, '专利撤回成功');

  } catch (error) {
    console.error('Error withdrawing patent:', error);
    throw error;
  }
};

/**
 * Restore patent to trading
 */
const restorePatent = async (req, res) => {
  try {
    const { id } = req.params;
    const patentId = parseInt(id);

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Get patent details
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check authorization - only current patent owner or admin can restore
    if (req.userRole !== 'admin' &&
        req.userAddress.toLowerCase() !== patent.currentOwnerAddress.toLowerCase()) {
      throw new AuthorizationError('You can only restore patents you currently own');
    }

    // Check if patent can be restored (must be withdrawn)
    if (patent.status !== 4) { // 4=withdrawn
      throw new ValidationError('Patent must be withdrawn to be restored');
    }

    // Restore patent
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'restorePatent',
      [patentId],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: '专利恢复成功'
    }, '专利恢复成功');

  } catch (error) {
    console.error('Error restoring patent:', error);
    throw error;
  }
};

/**
 * View patent document (returns URL for viewing in browser)
 */
const viewPatentDocument = async (req, res) => {
  try {
    const { id, documentType } = req.params;
    const patentId = parseInt(id);

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Validate document type
    const validDocumentTypes = ['patent', 'certificate', 'proxy'];
    if (!validDocumentTypes.includes(documentType)) {
      throw new ValidationError('Invalid document type. Must be: patent, certificate, or proxy');
    }

    // Get patent details
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if user has permission to view
    const isOwner = req.userAddress && req.userAddress.toLowerCase() === patent.uploaderAddress.toLowerCase();
    const isAdminOrReviewer = req.userRole === 'admin' || req.userRole === 'reviewer';
    const patentStatus = parseInt(patent.status);
    const isApprovedPatent = patentStatus === 1 || patentStatus === 3; // approved or normal

    if (!isOwner && !isAdminOrReviewer && !isApprovedPatent) {
      throw new AuthorizationError('You do not have permission to view this document');
    }

    // Get the appropriate document hash
    let documentHash;
    switch (documentType) {
      case 'patent':
        documentHash = patent.documentHash;
        break;
      case 'certificate':
        documentHash = patent.ownershipDocumentHash;
        break;
      case 'proxy':
        if (!patent.isAgentSale) {
          throw new NotFoundError('Proxy document not available for this patent');
        }
        documentHash = patent.ownershipDocumentHash; // For now, use ownership doc
        break;
      default:
        throw new ValidationError('Invalid document type');
    }

    if (!documentHash) {
      throw new NotFoundError('Document not found');
    }

    // Increment view count
    try {
      await blockchainService.callContractMethod(
        'PatentRegistry',
        'incrementViewCount',
        [patentId],
        { send: true, from: req.userAddress || '******************************************' }
      );
    } catch (error) {
      console.warn('Failed to increment view count:', error);
    }

    // Get IPFS gateway URL
    const ipfsGatewayUrl = `${ipfsService.gatewayUrl}/ipfs/${documentHash}`;

    // Return the URL for frontend to open in new tab/iframe
    res.success({
      url: ipfsGatewayUrl,
      documentType,
      documentHash,
      patentId: patentId.toString()
    }, 'Document URL retrieved successfully');

  } catch (error) {
    console.error('Error viewing patent document:', error);
    throw error;
  }
};

/**
 * Download patent document
 */
const downloadPatentDocument = async (req, res) => {
  try {
    const { id, documentType } = req.params;
    const patentId = parseInt(id);

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Validate document type
    const validDocumentTypes = ['patent', 'certificate', 'proxy'];
    if (!validDocumentTypes.includes(documentType)) {
      throw new ValidationError('Invalid document type. Must be: patent, certificate, or proxy');
    }

    // Get patent details
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentId]
    );

    if (!patent || patent.id.toString() !== id) {
      throw new NotFoundError('Patent not found');
    }

    // Check if user has permission to download
    const isOwner = req.userAddress && req.userAddress.toLowerCase() === patent.uploaderAddress.toLowerCase();
    const isAdminOrReviewer = req.userRole === 'admin' || req.userRole === 'reviewer';
    const patentStatus = parseInt(patent.status);
    const isApprovedPatent = patentStatus === 1 || patentStatus === 3; // approved or normal

    if (!isOwner && !isAdminOrReviewer && !isApprovedPatent) {
      throw new AuthorizationError('You do not have permission to download this document');
    }

    // Get the appropriate document hash
    let documentHash;
    let filename;
    switch (documentType) {
      case 'patent':
        documentHash = patent.documentHash;
        filename = `patent_${patentId}_document.pdf`;
        break;
      case 'certificate':
        documentHash = patent.ownershipDocumentHash;
        filename = `patent_${patentId}_certificate.pdf`;
        break;
      case 'proxy':
        if (!patent.isAgentSale) {
          throw new NotFoundError('Proxy document not available for this patent');
        }
        documentHash = patent.ownershipDocumentHash; // For now, use ownership doc
        filename = `patent_${patentId}_proxy.pdf`;
        break;
      default:
        throw new ValidationError('Invalid document type');
    }

    if (!documentHash) {
      throw new NotFoundError('Document not found');
    }

    // Increment download count
    try {
      await blockchainService.callContractMethod(
        'PatentRegistry',
        'incrementDownloadCount',
        [patentId],
        { send: true, from: req.userAddress || '******************************************' }
      );
    } catch (error) {
      console.warn('Failed to increment download count:', error);
    }

    try {
      // Try to download the file from IPFS and serve it
      const fileBuffer = await ipfsService.downloadFile(documentHash);

      // Set appropriate headers for file download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
      res.setHeader('Content-Length', fileBuffer.length);

      // Send the file buffer
      res.send(fileBuffer);

    } catch (ipfsError) {
      console.warn('Failed to download from IPFS, falling back to gateway redirect:', ipfsError);

      // Fallback to IPFS gateway redirect
      const ipfsGatewayUrl = `${ipfsService.gatewayUrl}/ipfs/${documentHash}`;
      res.redirect(ipfsGatewayUrl);
    }

  } catch (error) {
    console.error('Error downloading patent document:', error);
    throw error;
  }
};

/**
 * Increment patent view count
 */
const incrementViewCount = async (req, res) => {
  try {
    const { id } = req.params;
    const patentId = parseInt(id);

    if (isNaN(patentId) || patentId < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    // Increment view count
    const result = await blockchainService.callContractMethod(
      'PatentRegistry',
      'incrementViewCount',
      [patentId],
      { send: true, from: req.userAddress }
    );

    res.success({
      transactionHash: result.transactionHash,
      patentId: id,
      message: 'View count incremented'
    });

  } catch (error) {
    console.error('Error incrementing view count:', error);
    throw error;
  }
};

module.exports = {
  uploadPatent,
  searchPatents,
  getPatentDetails,
  getUserPatents,
  getUserOwnedPatents,
  withdrawPatent,
  restorePatent,
  viewPatentDocument,
  downloadPatentDocument,
  incrementViewCount
};
