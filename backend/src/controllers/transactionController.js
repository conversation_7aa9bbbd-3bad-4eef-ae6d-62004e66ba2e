const blockchainService = require('../services/blockchainService');
const { formatDate, formatAddress, parsePaginationParams, createPagination } = require('../middleware/responseFormatter');
const { NotFoundError, ValidationError, AuthorizationError } = require('../middleware/errorHandler');

/**
 * Transaction Controller
 * Handles transaction management operations
 */

/**
 * Initiate a patent purchase transaction
 */
const initiateTransaction = async (req, res) => {
  try {
    const { patentId, sellerAddress, price } = req.body;
    const buyerAddress = req.userAddress;

    console.log('🚀 Initiating transaction:', {
      patentId,
      sellerAddress,
      price,
      buyerAddress,
      body: req.body
    });

    // Validate inputs
    if (!patentId || !sellerAddress || !price) {
      throw new ValidationError('Patent ID, seller address, and price are required');
    }

    if (!blockchainService.web3.utils.isAddress(sellerAddress)) {
      throw new ValidationError('Invalid seller address format');
    }

    if (buyerAddress.toLowerCase() === sellerAddress.toLowerCase()) {
      throw new ValidationError('Cannot purchase your own patent');
    }

    const patentIdNum = parseInt(patentId);
    if (isNaN(patentIdNum) || patentIdNum < 0) {
      throw new ValidationError('Invalid patent ID');
    }

    console.log('✅ Input validation passed');

    // Verify patent exists and is available for trading
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [patentIdNum]
    );

    console.log('📋 Patent details:', patent);

    if (!patent || patent.id.toString() !== patentId.toString()) {
      throw new NotFoundError('Patent not found');
    }

    // Check if patent is available for trading
    // Handle both numeric status and string status
    let statusValue;
    if (typeof patent.status === 'string') {
      statusValue = patent.status;
    } else if (typeof patent.status === 'number') {
      statusValue = patent.status;
    } else if (patent.status && typeof patent.status.toString === 'function') {
      // Handle BigNumber or other objects with toString method
      statusValue = parseInt(patent.status.toString());
    } else {
      statusValue = String(patent.status);
    }

    let isAvailableForTrading = false;

    if (typeof statusValue === 'number') {
      // Numeric status: 1=approved, 3=normal
      isAvailableForTrading = (statusValue === 1 || statusValue === 3);
    } else if (typeof statusValue === 'string') {
      // String status: 'approved', 'normal'
      isAvailableForTrading = (statusValue === 'approved' || statusValue === 'normal');
    }

    console.log('🔍 Patent status check:', {
      statusValue,
      statusType: typeof statusValue,
      isAvailableForTrading
    });

    if (!isAvailableForTrading) {
      throw new ValidationError('Patent is not available for trading');
    }

    // Verify seller is the patent owner
    if (patent.uploaderAddress.toLowerCase() !== sellerAddress.toLowerCase()) {
      throw new ValidationError('Seller is not the patent owner');
    }

    console.log('✅ Patent validation passed');

    // Convert price to Wei
    const priceInWei = blockchainService.web3.utils.toWei(price.toString(), 'ether');

    console.log('💰 Price conversion:', {
      originalPrice: price,
      priceInWei: priceInWei
    });

    // Initiate transaction on blockchain
    console.log('📝 Calling blockchain contract...');
    const result = await blockchainService.callContractMethod(
      'TransactionManager',
      'initiateTransaction',
      [patentIdNum, sellerAddress, priceInWei],
      { send: true, from: buyerAddress }
    );

    console.log('✅ Blockchain transaction result:', result);

    // Extract transaction ID from events
    let transactionId = null;
    if (result.events && result.events.TransactionInitiated) {
      transactionId = result.events.TransactionInitiated.returnValues.transactionId;
    }

    // Set patent status to "trading" to prevent other purchases
    try {
      await blockchainService.callContractMethod(
        'PatentRegistry',
        'setTradingStatus',
        [patentIdNum, true],
        { send: true, from: buyerAddress }
      );
      console.log('✅ Patent status set to trading');
    } catch (statusError) {
      console.warn('⚠️ Failed to set patent trading status:', statusError);
      // Continue - this is not critical for the transaction
    }

    res.success({
      transactionId: transactionId || 'unknown',
      transactionHash: result.transactionHash,
      message: '专利购买申请已提交，等待审核'
    }, '专利购买申请已提交');

  } catch (error) {
    console.error('❌ Error initiating transaction:', error);
    throw error;
  }
};

/**
 * Get user's transaction history
 */
const getUserTransactions = async (req, res) => {
  try {
    const { address } = req.params;
    const { type, status } = req.query;

    // Validate address
    if (!blockchainService.web3.utils.isAddress(address)) {
      throw new ValidationError('Invalid address format');
    }

    // Check authorization
    if (req.userRole === 'user' && req.userAddress.toLowerCase() !== address.toLowerCase()) {
      throw new AuthorizationError('You can only view your own transactions');
    }

    const { page, limit, offset } = parsePaginationParams(req);

    // Get user's transactions from blockchain
    const buyerTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getBuyerTransactions',
      [address]
    );

    const sellerTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getSellerTransactions',
      [address]
    );

    const allTransactionIds = [...buyerTransactions, ...sellerTransactions];
    const transactions = [];

    // Get details for each transaction
    for (const txId of allTransactionIds) {
      try {
        const transaction = await blockchainService.callContractMethod(
          'TransactionManager',
          'getTransaction',
          [txId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [transaction.patentId]
        );

        // Get user profiles
        let buyerName = 'Unknown';
        let sellerName = 'Unknown';
        try {
          const buyerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.buyerAddress]
          );
          buyerName = buyerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get buyer profile:', error);
        }

        try {
          const sellerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.sellerAddress]
          );
          sellerName = sellerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get seller profile:', error);
        }

        const statusNames = ['pending', 'approved', 'rejected', 'completed', 'cancelled'];
        const userType = transaction.buyerAddress.toLowerCase() === address.toLowerCase() ? 'purchase' : 'sale';

        // Apply filters
        if (type && type !== userType) continue;
        if (status && statusNames[transaction.status] !== status) continue;

        transactions.push({
          id: transaction.id.toString(),
          patentId: transaction.patentId.toString(),
          patentName: patent.name,
          patentNumber: patent.number,
          buyerAddress: formatAddress(transaction.buyerAddress),
          buyerName,
          sellerAddress: formatAddress(transaction.sellerAddress),
          sellerName,
          price: blockchainService.web3.utils.fromWei(transaction.price, 'ether'),
          submitDate: formatDate(new Date(parseInt(transaction.submitDate) * 1000)),
          status: statusNames[transaction.status] || 'unknown',
          type: userType
        });
      } catch (error) {
        console.warn(`Failed to get transaction details for ID ${txId}:`, error);
        continue;
      }
    }

    // Sort by submit date (newest first)
    transactions.sort((a, b) => new Date(b.submitDate) - new Date(a.submitDate));

    // Apply pagination
    const paginatedTransactions = transactions.slice(offset, offset + limit);
    const pagination = createPagination(page, limit, transactions.length);

    res.success({
      transactions: paginatedTransactions,
      pagination
    });

  } catch (error) {
    console.error('Error getting user transactions:', error);
    throw error;
  }
};

/**
 * Get pending transactions for review
 */
const getPendingTransactions = async (req, res) => {
  try {
    // Check authorization - only reviewers and admins can view pending transactions
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can view pending transactions');
    }

    // Get pending transactions from blockchain
    const pendingTransactions = await blockchainService.callContractMethod(
      'TransactionManager',
      'getPendingTransactions',
      [],
      { from: req.userAddress }
    );

    const transactions = [];

    // Get details for each pending transaction
    for (const txId of pendingTransactions) {
      try {
        const transaction = await blockchainService.callContractMethod(
          'TransactionManager',
          'getTransaction',
          [txId]
        );

        // Get patent details
        const patent = await blockchainService.callContractMethod(
          'PatentRegistry',
          'getPatent',
          [transaction.patentId]
        );

        // Get user profiles
        let buyerName = 'Unknown';
        let sellerName = 'Unknown';
        try {
          const buyerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.buyerAddress]
          );
          buyerName = buyerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get buyer profile:', error);
        }

        try {
          const sellerProfile = await blockchainService.callContractMethod(
            'UserManagement',
            'getUserProfile',
            [transaction.sellerAddress]
          );
          sellerName = sellerProfile.name || 'Unknown';
        } catch (error) {
          console.warn('Failed to get seller profile:', error);
        }

        transactions.push({
          id: transaction.id.toString(),
          patentId: transaction.patentId.toString(),
          patentName: patent.name,
          patentNumber: patent.number,
          buyerAddress: formatAddress(transaction.buyerAddress),
          buyerName,
          sellerAddress: formatAddress(transaction.sellerAddress),
          sellerName,
          price: blockchainService.web3.utils.fromWei(transaction.price, 'ether'),
          submitDate: formatDate(new Date(parseInt(transaction.submitDate) * 1000)),
          status: 'pending',
          type: 'purchase'
        });
      } catch (error) {
        console.warn(`Failed to get transaction details for ID ${txId}:`, error);
        continue;
      }
    }

    // Sort by submit date (oldest first for review queue)
    transactions.sort((a, b) => new Date(a.submitDate) - new Date(b.submitDate));

    res.success({ data: transactions });

  } catch (error) {
    console.error('Error getting pending transactions:', error);
    throw error;
  }
};

/**
 * Approve a transaction (reviewer/admin only)
 */
const approveTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, comments } = req.body;
    const transactionId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can approve transactions');
    }

    if (isNaN(transactionId) || transactionId < 0) {
      throw new ValidationError('Invalid transaction ID');
    }

    // Get transaction details
    const transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );

    if (!transaction || transaction.id.toString() !== id) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if transaction is pending
    if (parseInt(transaction.status) !== 0) { // 0 = pending
      throw new ValidationError('Transaction is not pending approval');
    }

    // Get patent details to verify ownership transfer
    const patent = await blockchainService.callContractMethod(
      'PatentRegistry',
      'getPatent',
      [transaction.patentId]
    );

    if (!patent) {
      throw new NotFoundError('Associated patent not found');
    }

    console.log('🔄 Approving transaction:', {
      transactionId: id,
      patentId: transaction.patentId.toString(),
      buyer: transaction.buyerAddress,
      seller: transaction.sellerAddress,
      price: blockchainService.web3.utils.fromWei(transaction.price, 'ether')
    });

    // Step 1: Approve transaction on blockchain
    const approveResult = await blockchainService.callContractMethod(
      'TransactionManager',
      'approveTransaction',
      [transactionId, comments || 'Transaction approved by reviewer'],
      { send: true, from: req.userAddress }
    );

    console.log('✅ Transaction approved on blockchain:', approveResult.transactionHash);

    // Step 2: Automatically complete ownership transfer
    console.log('🔄 Transferring patent ownership...');
    
    try {
      // Transfer patent ownership to buyer
      const transferResult = await blockchainService.callContractMethod(
        'PatentRegistry',
        'transferOwnership',
        [transaction.patentId, transaction.buyerAddress],
        { send: true, from: req.userAddress }
      );

      console.log('✅ Patent ownership transferred:', transferResult.transactionHash);

      // Step 3: Complete the transaction
      const completeResult = await blockchainService.callContractMethod(
        'TransactionManager',
        'completeTransaction',
        [transactionId],
        { send: true, from: req.userAddress }
      );

      console.log('✅ Transaction completed:', completeResult.transactionHash);

      // Step 4: Clear patent trading status
      await blockchainService.callContractMethod(
        'PatentRegistry',
        'setTradingStatus',
        [transaction.patentId, false],
        { send: true, from: req.userAddress }
      );

      console.log('✅ Patent trading status cleared');

      res.success({
        transactionHash: approveResult.transactionHash,
        transferHash: transferResult.transactionHash,
        completeHash: completeResult.transactionHash,
        transactionId: id,
        message: '交易已批准并完成，专利权已转移'
      }, '交易已批准并完成，专利权已转移');

    } catch (transferError) {
      console.error('❌ Failed to complete ownership transfer:', transferError);
      
      // If ownership transfer fails, we should revert the approval
      // For now, we'll just return an error indicating partial completion
      res.success({
        transactionHash: approveResult.transactionHash,
        transactionId: id,
        message: '交易已批准，但专利权转移失败，请手动完成',
        warning: true
      }, '交易已批准，但专利权转移失败');
    }

  } catch (error) {
    console.error('Error approving transaction:', error);
    throw error;
  }
};

/**
 * Reject a transaction (reviewer/admin only)
 */
const rejectTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const { reviewerAddress, reason } = req.body;
    const transactionId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can reject transactions');
    }

    if (isNaN(transactionId) || transactionId < 0) {
      throw new ValidationError('Invalid transaction ID');
    }

    // Get transaction details
    const transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );

    if (!transaction || transaction.id.toString() !== id) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if transaction is pending
    if (parseInt(transaction.status) !== 0) { // 0 = pending
      throw new ValidationError('Transaction is not pending approval');
    }

    console.log('❌ Rejecting transaction:', {
      transactionId: id,
      patentId: transaction.patentId.toString(),
      buyer: transaction.buyerAddress,
      seller: transaction.sellerAddress,
      reason: reason || 'No reason provided'
    });

    // Reject transaction on blockchain
    const result = await blockchainService.callContractMethod(
      'TransactionManager',
      'rejectTransaction',
      [transactionId, reason || 'Transaction rejected by reviewer'],
      { send: true, from: req.userAddress }
    );

    console.log('✅ Transaction rejected on blockchain:', result.transactionHash);

    // Clear patent trading status to make it available for purchase again
    try {
      await blockchainService.callContractMethod(
        'PatentRegistry',
        'setTradingStatus',
        [transaction.patentId, false],
        { send: true, from: req.userAddress }
      );
      console.log('✅ Patent trading status cleared');
    } catch (statusError) {
      console.warn('⚠️ Failed to clear patent trading status:', statusError);
      // Continue - this is not critical
    }

    res.success({
      transactionHash: result.transactionHash,
      transactionId: id,
      message: '交易已拒绝'
    }, '交易已拒绝');

  } catch (error) {
    console.error('Error rejecting transaction:', error);
    throw error;
  }
};

/**
 * Complete a transaction by transferring ownership (reviewer/admin only)
 */
const completeTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const transactionId = parseInt(id);

    // Check authorization
    if (req.userRole !== 'reviewer' && req.userRole !== 'admin') {
      throw new AuthorizationError('Only reviewers and admins can complete transactions');
    }

    if (isNaN(transactionId) || transactionId < 0) {
      throw new ValidationError('Invalid transaction ID');
    }

    // Get transaction details
    const transaction = await blockchainService.callContractMethod(
      'TransactionManager',
      'getTransaction',
      [transactionId]
    );

    if (!transaction || transaction.id.toString() !== id) {
      throw new NotFoundError('Transaction not found');
    }

    // Check if transaction is approved
    if (parseInt(transaction.status) !== 1) { // 1 = approved
      throw new ValidationError('Transaction is not approved');
    }

    console.log('🔄 Completing transaction:', {
      transactionId: id,
      patentId: transaction.patentId.toString(),
      buyer: transaction.buyerAddress,
      seller: transaction.sellerAddress,
      price: blockchainService.web3.utils.fromWei(transaction.price, 'ether')
    });

    // Step 1: Transfer patent ownership
    const transferResult = await blockchainService.callContractMethod(
      'PatentRegistry',
      'transferOwnership',
      [transaction.patentId, transaction.buyerAddress],
      { send: true, from: req.userAddress }
    );

    console.log('✅ Patent ownership transferred:', transferResult.transactionHash);

    // Step 2: Complete the transaction with blockchain hash
    await blockchainService.callContractMethod(
      'TransactionManager',
      'completeTransaction',
      [transactionId, transferResult.transactionHash],
      { send: true, from: req.userAddress }
    );

    console.log('✅ Transaction completed with ownership transfer');

    res.success({
      transactionHash: transferResult.transactionHash,
      transactionId: id,
      message: '交易已完成，专利转让成功'
    }, '交易已完成，专利转让成功');

  } catch (error) {
    console.error('Error completing transaction:', error);
    throw error;
  }
};

/**
 * Get transaction details
 */
const getTransactionDetails = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Transaction details endpoint - implementation pending' });
};

/**
 * Cancel a transaction
 */
const cancelTransaction = async (req, res) => {
  // Placeholder implementation
  res.success({ message: 'Transaction cancel endpoint - implementation pending' });
};

module.exports = {
  initiateTransaction,
  getUserTransactions,
  getPendingTransactions,
  approveTransaction,
  rejectTransaction,
  completeTransaction,
  getTransactionDetails,
  cancelTransaction
};
