#!/usr/bin/env node

/**
 * Test script to verify patent ownership display and categorization fixes
 * This script tests:
 * 1. Patent categorization in MyPatentsView (uploaded vs sold patents)
 * 2. Ownership display in PatentSearchView
 * 3. Purchase button logic based on ownership status
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000/api';

// Test configuration
const TEST_CONFIG = {
  // Using actual addresses from Ganache setup
  user1Address: '******************************************', // Original patent owner
  user2Address: '******************************************', // Patent buyer
  reviewerAddress: '******************************************', // Reviewer
  patentId: '6' // Using available patent ID
};

// Helper function to make API calls
async function apiCall(method, endpoint, data = null, userAddress = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (userAddress) {
      config.headers['X-User-Address'] = userAddress;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return response.data;
  } catch (error) {
    console.error(`❌ API call failed: ${method} ${endpoint}`);
    console.error('Error:', error.response?.data || error.message);
    throw error;
  }
}

// Test 1: Verify patent categorization for original owner
async function testPatentCategorizationOriginalOwner() {
  console.log('\n🧪 Test 1: Patent Categorization for Original Owner');
  console.log('=' * 60);

  try {
    // Get user's uploaded patents
    const uploadedPatents = await apiCall('GET', `/patents/user/${TEST_CONFIG.user1Address}`, null, TEST_CONFIG.user1Address);
    console.log(`📋 User1 uploaded patents: ${uploadedPatents.data?.length || 0}`);

    // Get user's transactions to see sold patents
    const transactions = await apiCall('GET', `/transactions/user/${TEST_CONFIG.user1Address}`, null, TEST_CONFIG.user1Address);
    console.log(`📋 User1 transactions: ${transactions.data?.transactions?.length || 0}`);

    // Filter completed sales where user1 was the seller
    const completedSales = transactions.data?.transactions?.filter(tx => 
      tx.sellerAddress && 
      tx.sellerAddress.toLowerCase().includes(TEST_CONFIG.user1Address.toLowerCase().slice(2, 8)) &&
      tx.status === 'completed'
    ) || [];

    console.log(`📋 User1 completed sales: ${completedSales.length}`);

    if (completedSales.length > 0) {
      console.log('✅ Found sold patents - categorization should work correctly');
      completedSales.forEach((sale, index) => {
        console.log(`   Sale ${index + 1}: Patent ${sale.patentId} sold to ${sale.buyerAddress}`);
      });
    } else {
      console.log('⚠️ No completed sales found - may need to complete a transaction first');
    }

  } catch (error) {
    console.error('❌ Test 1 failed:', error.message);
  }
}

// Test 2: Verify patent categorization for buyer
async function testPatentCategorizationBuyer() {
  console.log('\n🧪 Test 2: Patent Categorization for Buyer');
  console.log('=' * 60);

  try {
    // Get user's uploaded patents (should be empty or only their own uploads)
    const uploadedPatents = await apiCall('GET', `/patents/user/${TEST_CONFIG.user2Address}`, null, TEST_CONFIG.user2Address);
    console.log(`📋 User2 uploaded patents: ${uploadedPatents.data?.length || 0}`);

    // Get user's transactions to see purchased patents
    const transactions = await apiCall('GET', `/transactions/user/${TEST_CONFIG.user2Address}`, null, TEST_CONFIG.user2Address);
    console.log(`📋 User2 transactions: ${transactions.data?.transactions?.length || 0}`);

    // Filter completed purchases where user2 was the buyer
    const completedPurchases = transactions.data?.transactions?.filter(tx => 
      tx.buyerAddress && 
      tx.buyerAddress.toLowerCase().includes(TEST_CONFIG.user2Address.toLowerCase().slice(2, 8)) &&
      tx.status === 'completed'
    ) || [];

    console.log(`📋 User2 completed purchases: ${completedPurchases.length}`);

    if (completedPurchases.length > 0) {
      console.log('✅ Found purchased patents - categorization should work correctly');
      completedPurchases.forEach((purchase, index) => {
        console.log(`   Purchase ${index + 1}: Patent ${purchase.patentId} bought from ${purchase.sellerAddress}`);
      });
    } else {
      console.log('⚠️ No completed purchases found - may need to complete a transaction first');
    }

  } catch (error) {
    console.error('❌ Test 2 failed:', error.message);
  }
}

// Test 3: Verify ownership display in search results
async function testOwnershipDisplayInSearch() {
  console.log('\n🧪 Test 3: Ownership Display in Search Results');
  console.log('=' * 60);

  try {
    // Search for patents
    const searchResults = await apiCall('GET', '/patents/search?limit=10');
    console.log(`📋 Search results: ${searchResults.data?.patents?.length || 0} patents found`);

    if (searchResults.data?.patents?.length > 0) {
      console.log('✅ Patents found in search results');
      
      // Check each patent's ownership information
      searchResults.data.patents.forEach((patent, index) => {
        console.log(`\n   Patent ${index + 1}:`);
        console.log(`     - ID: ${patent.id}`);
        console.log(`     - Name: ${patent.name}`);
        console.log(`     - Current Owner: ${patent.uploaderName || 'Unknown'}`);
        console.log(`     - Owner Address: ${patent.uploaderAddress}`);
        console.log(`     - Status: ${patent.status}`);
        
        // Check if this patent has been transferred
        if (patent.id === TEST_CONFIG.patentId) {
          console.log(`     ⭐ This is our test patent - checking ownership...`);
        }
      });
    } else {
      console.log('⚠️ No patents found in search results');
    }

  } catch (error) {
    console.error('❌ Test 3 failed:', error.message);
  }
}

// Test 4: Verify purchase button logic
async function testPurchaseButtonLogic() {
  console.log('\n🧪 Test 4: Purchase Button Logic');
  console.log('=' * 60);

  try {
    // Get specific patent details
    const patentDetails = await apiCall('GET', `/patents/${TEST_CONFIG.patentId}`);
    
    if (patentDetails.data?.data) {
      const patent = patentDetails.data.data;
      console.log(`📋 Patent ${TEST_CONFIG.patentId} details:`);
      console.log(`     - Name: ${patent.name}`);
      console.log(`     - Current Owner: ${patent.uploaderName || 'Unknown'}`);
      console.log(`     - Owner Address: ${patent.uploaderAddress}`);
      console.log(`     - Status: ${patent.status}`);
      
      // Test purchase logic for different users
      console.log('\n🔍 Purchase button logic tests:');
      
      // Test 1: Owner trying to buy their own patent
      const isOwner = patent.uploaderAddress.toLowerCase() === TEST_CONFIG.user1Address.toLowerCase();
      console.log(`     - User1 is owner: ${isOwner} (should NOT be able to purchase)`);
      
      // Test 2: Different user trying to buy
      const canUser2Buy = patent.uploaderAddress.toLowerCase() !== TEST_CONFIG.user2Address.toLowerCase();
      console.log(`     - User2 can buy: ${canUser2Buy} (should be able to purchase if available)`);
      
      // Test 3: Status check
      const isAvailable = patent.status === 'normal' || patent.status === 'approved';
      console.log(`     - Patent available: ${isAvailable} (status: ${patent.status})`);
      
      if (isAvailable && canUser2Buy) {
        console.log('✅ Purchase button should be enabled for User2');
      } else {
        console.log('⚠️ Purchase button should be disabled');
      }
      
    } else {
      console.log('⚠️ Could not get patent details');
    }

  } catch (error) {
    console.error('❌ Test 4 failed:', error.message);
  }
}

// Main test function
async function runOwnershipTests() {
  console.log('🧪 Testing Patent Ownership Display and Categorization Fixes');
  console.log('==============================================================');
  console.log(`📋 Test Configuration:`);
  console.log(`   - User1 (Original Owner): ${TEST_CONFIG.user1Address}`);
  console.log(`   - User2 (Buyer): ${TEST_CONFIG.user2Address}`);
  console.log(`   - Reviewer: ${TEST_CONFIG.reviewerAddress}`);
  console.log(`   - Test Patent ID: ${TEST_CONFIG.patentId}`);

  try {
    await testPatentCategorizationOriginalOwner();
    await testPatentCategorizationBuyer();
    await testOwnershipDisplayInSearch();
    await testPurchaseButtonLogic();

    console.log('\n🎉 All ownership tests completed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Patent categorization logic updated');
    console.log('   ✅ Ownership display should work correctly');
    console.log('   ✅ Purchase button logic should work correctly');
    console.log('\n💡 Next steps:');
    console.log('   1. Test the frontend UI to verify the fixes');
    console.log('   2. Complete a transaction to test the full workflow');
    console.log('   3. Verify patents move between categories correctly');

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runOwnershipTests().catch(console.error);
}

module.exports = {
  runOwnershipTests,
  testPatentCategorizationOriginalOwner,
  testPatentCategorizationBuyer,
  testOwnershipDisplayInSearch,
  testPurchaseButtonLogic
}; 